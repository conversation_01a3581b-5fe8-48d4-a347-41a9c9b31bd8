---
description: 
globs: 
alwaysApply: false
---
# 项目结构概述

本项目是一个名为"选码助手"的Android应用程序，使用Kotlin和Jetpack Compose开发。

## 主要文件结构

- [MainActivity.kt](mdc:app/src/main/java/com/example/myxuanmazhushou/MainActivity.kt) - 应用程序的主界面，包含彩票号码选择和筛选功能
- [PredictionActivity.kt](mdc:app/src/main/java/com/example/myxuanmazhushou/PredictionActivity.kt) - 预测分析界面，提供历史数据统计和分析功能
- [HistoryActivity.kt](mdc:app/src/main/java/com/example/myxuanmazhushou/HistoryActivity.kt) - 历史记录界面，显示历史开奖数据

## 工具类和辅助功能

- [ZodiacUtils.kt](mdc:app/src/main/java/com/example/myxuanmazhushou/utils/ZodiacUtils.kt) - 生肖工具类，提供生肖映射和相关功能
- [ZodiacStatsEngine.kt](mdc:app/src/main/java/com/example/myxuanmazhushou/stats/ZodiacStatsEngine.kt) - 统计引擎，用于分析历史数据

## 应用配置

- [AndroidManifest.xml](mdc:app/src/main/AndroidManifest.xml) - 应用程序清单文件，定义了应用的基本配置和权限
