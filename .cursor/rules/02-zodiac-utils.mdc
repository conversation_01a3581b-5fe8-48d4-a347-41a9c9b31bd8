---
description: 
globs: 
alwaysApply: false
---
# 生肖工具类使用指南

[ZodiacUtils.kt](mdc:app/src/main/java/com/example/myxuanmazhushou/utils/ZodiacUtils.kt) 是应用程序中处理生肖映射的核心工具类，提供以下功能：

## 主要常量

- `ZODIAC_ORDER` - 十二生肖的顺序列表（鼠、牛、虎、兔、龙、蛇、马、羊、猴、鸡、狗、猪）
- `RED_NUMBERS`, `BLUE_NUMBERS`, `GREEN_NUMBERS` - 波色分类号码集合
- `DOMESTIC_ANIMALS`, `WILD_ANIMALS` - 家禽和野兽分类
- `ODD_ZODIACS`, `EVEN_ZODIACS` - 单肖和双肖分类

## 主要方法

- `generateZodiacMapping(year: Int)` - 根据指定年份生成生肖与号码的映射关系
- `getZodiacMappings()` - 获取当前使用的生肖映射
- `getZodiacForNumber(number: Int)` - 获取指定号码对应的生肖
- `updateBaseYear(context: Context, year: Int)` - 更新基准年份并重新生成映射

## 使用示例

在需要使用生肖工具类的地方，首先需要加载保存的映射：

```kotlin
// 初始化生肖映射
ZodiacUtils.loadSavedMappings(context)

// 获取号码对应的生肖
val zodiac = ZodiacUtils.getZodiacForNumber(25)
```
