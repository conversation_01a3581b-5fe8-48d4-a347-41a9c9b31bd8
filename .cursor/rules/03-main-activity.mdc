---
description: 
globs: 
alwaysApply: false
---
# 主界面组件指南

[MainActivity.kt](mdc:app/src/main/java/com/example/myxuanmazhushou/MainActivity.kt) 是应用程序的主界面，使用 Jetpack Compose 构建。

## 主要数据类

- `LotteryResult` - 彩票开奖结果数据类
- `BetNumber` - 投注号码数据类
- `BetCartItem` - 购物车项目数据类

## 投注类型

应用支持多种投注类型，定义在 `BetType` 枚举中：

- `SPECIAL_NUMBER` - 特码
- `TWO_IN_TWO` - 二中二
- `TWO_IN_TWO_COMPLEX` - 二中二复式
- `THREE_IN_THREE` - 三中三
- `THREE_IN_THREE_COMPLEX` - 三中三复式
- `TWO_IN_THREE` - 三中二
- `TWO_IN_THREE_COMPLEX` - 三中二复式

## 主要组件

- `MainScreen` - 主界面组合函数
- 筛选功能 - 支持按波色、大小、单双、合数、尾数、生肖等多种方式筛选号码

## 使用提示

主界面使用了响应式布局设计，可以适应手机和平板设备：

```kotlin
// 检测是否为平板设备
val isTabletDevice = isTablet()
```
