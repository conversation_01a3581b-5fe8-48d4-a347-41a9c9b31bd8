---
description: 
globs: 
alwaysApply: false
---
# 预测分析界面指南

[PredictionActivity.kt](mdc:app/src/main/java/com/example/myxuanmazhushou/PredictionActivity.kt) 是应用程序的预测分析界面，提供历史数据统计和分析功能。

## 主要功能

- 历史数据管理 - 加载、保存和分析历史开奖数据
- 生肖统计 - 统计特码生肖出现频率
- 号码统计 - 统计特码号码出现频率
- 按年份和月份的统计分析

## 统计引擎

预测分析界面使用 [ZodiacStatsEngine.kt](mdc:app/src/main/java/com/example/myxuanmazhushou/stats/ZodiacStatsEngine.kt) 进行数据统计分析：

- `countSpecialZodiacOccurrences` - 统计特码生肖出现次数
- `countSpecialZodiacByYear` - 按年份统计特码生肖
- `countSpecialZodiacByMonth` - 按月份统计特码生肖
- `countSpecialNumberOccurrences` - 统计特码号码出现次数
- `countSpecialNumberByYear` - 按年份统计特码号码
- `countSpecialNumberByMonth` - 按月份统计特码号码

## 文件存储

应用支持将历史数据保存到设备存储：

```kotlin
// 默认保存目录
private val lotteryDataDir by lazy {
    File(Environment.getExternalStorageDirectory(), "历史数据")
}
```
