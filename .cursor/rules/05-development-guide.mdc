---
description: 
globs: 
alwaysApply: false
---
# 开发环境配置指南

## 项目配置

本项目是一个使用 Kotlin 和 Jetpack Compose 开发的 Android 应用程序。

- 构建系统：Gradle (Kotlin DSL)
- 主要配置文件：[build.gradle.kts](mdc:build.gradle.kts) 和 [app/build.gradle.kts](mdc:app/build.gradle.kts)
- 应用程序清单：[AndroidManifest.xml](mdc:app/src/main/AndroidManifest.xml)

## 依赖管理

项目依赖在 [app/build.gradle.kts](mdc:app/build.gradle.kts) 中定义，主要使用了以下技术：

- Jetpack Compose - 用于构建现代化的 UI
- Kotlin Coroutines - 用于异步操作
- AndroidX 库 - 提供各种 Android 开发工具

## 权限要求

应用程序需要以下权限：

- `INTERNET` - 用于网络访问
- `WRITE_EXTERNAL_STORAGE` 和 `READ_EXTERNAL_STORAGE` - 用于读写历史数据
- `MANAGE_EXTERNAL_STORAGE` - 用于管理外部存储（Android 11+）

## 开发提示

- 使用国内镜像源下载依赖、库和组件
- 在 Android 11+ 设备上需要特殊处理文件存储权限
- 使用 Jetpack Compose 预览功能测试 UI 组件
