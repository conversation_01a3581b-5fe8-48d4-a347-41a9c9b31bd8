package com.example.myxuanmazhushou

import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.slideInVertically
import androidx.compose.animation.slideOutVertically
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.ArrowForward
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.example.myxuanmazhushou.ui.theme.MyXuanmazhushouTheme
import com.example.myxuanmazhushou.utils.ZodiacUtils
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.net.URL

// 修改数据类以包含每个号码的生肖信息
data class LotteryNumber(
    val number: String,
    val zodiac: String
)

data class LotteryFullResult(
    val period: String,                // 期号
    val numbers: List<LotteryNumber>,  // 前6个号码及其生肖
    val specialNumber: LotteryNumber,  // 特码及其生肖
    val date: String                   // 开奖日期
)

class HistoryActivity : ComponentActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()
        
        setContent {
            MyXuanmazhushouTheme {
                HistoryScreen(onBackClick = { finish() })
            }
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun HistoryScreen(onBackClick: () -> Unit) {
    var historyData by remember { mutableStateOf<List<LotteryFullResult>>(emptyList()) }
    var isLoading by remember { mutableStateOf(true) }
    var currentPage by remember { mutableStateOf(1) }
    val itemsPerPage = 50
    val scope = rememberCoroutineScope()
    
    // 添加滚动状态
    val scrollState = rememberLazyListState()
    
    // 控制分页器可见性
    var isPaginationVisible by remember { mutableStateOf(false) }
    
    // 记录上一次的滚动位置和方向
    var lastScrollOffset by remember { mutableStateOf(0) }
    var isScrollingUp by remember { mutableStateOf(false) }
    
    // 使用更可靠的方法监听滚动方向
    LaunchedEffect(scrollState) {
        snapshotFlow { 
            Pair(scrollState.firstVisibleItemIndex, scrollState.firstVisibleItemScrollOffset) 
        }.collect { (firstVisibleItem, scrollOffset) ->
            // 计算当前滚动位置的"绝对偏移量"
            val currentScrollPosition = firstVisibleItem * 1000 + scrollOffset
            val previousScrollPosition = lastScrollOffset
            
            // 确定滚动方向
            isScrollingUp = currentScrollPosition < previousScrollPosition
            
            // 根据滚动方向更新分页器可见性
            isPaginationVisible = isScrollingUp
            
            // 更新上一次的滚动位置
            lastScrollOffset = currentScrollPosition
        }
    }
    
    // 获取历史数据
    LaunchedEffect(Unit) {
        scope.launch {
            fetchHistoryData(500) { data ->
                historyData = data
                isLoading = false
            }
        }
    }
    
    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text("开奖历史") },
                navigationIcon = {
                    IconButton(onClick = onBackClick) {
                        Icon(
                            imageVector = Icons.Default.ArrowBack,
                            contentDescription = "返回"
                        )
                    }
                }
            )
        }
    ) { innerPadding ->
        Box(
            modifier = Modifier
                .fillMaxSize()
                .padding(innerPadding)
        ) {
            if (isLoading) {
                CircularProgressIndicator(
                    modifier = Modifier.align(Alignment.Center)
                )
            } else {
                // 使用分页显示数据
                val displayedData = historyData.chunked(itemsPerPage)[currentPage - 1]
                
                Column(
                    modifier = Modifier.fillMaxSize()
                ) {
                    // 数据列表
                    LazyColumn(
                        modifier = Modifier.weight(1f),
                        contentPadding = PaddingValues(
                            start = 16.dp, 
                            end = 16.dp, 
                            top = 16.dp,
                            bottom = 72.dp // 增加底部内边距，为分页控制器留出空间
                        ),
                        verticalArrangement = Arrangement.spacedBy(12.dp),
                        state = scrollState // 使用滚动状态
                    ) {
                        items(displayedData) { result ->
                            HistoryItem(result)
                        }
                    }
                }
                
                // 分页控制器 - 使用动画显示/隐藏
                AnimatedVisibility(
                    visible = isPaginationVisible,
                    modifier = Modifier
                        .align(Alignment.BottomCenter)
                        .padding(bottom = 16.dp),
                    enter = fadeIn() + slideInVertically { it },
                    exit = fadeOut() + slideOutVertically { it }
                ) {
                    Card(
                        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp),
                        colors = CardDefaults.cardColors(
                            containerColor = MaterialTheme.colorScheme.surface
                        )
                    ) {
                        Row(
                            modifier = Modifier
                                .padding(horizontal = 16.dp, vertical = 8.dp),
                            horizontalArrangement = Arrangement.SpaceBetween,
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            IconButton(
                                onClick = { 
                                    if (currentPage > 1) {
                                        currentPage--
                                        // 滚动到顶部
                                        scope.launch {
                                            scrollState.scrollToItem(0)
                                        }
                                    }
                                },
                                enabled = currentPage > 1
                            ) {
                                Icon(
                                    imageVector = Icons.Default.ArrowBack,
                                    contentDescription = "上一页"
                                )
                            }
                            
                            Text(
                                text = "第 $currentPage/${(historyData.size + itemsPerPage - 1) / itemsPerPage} 页",
                                style = MaterialTheme.typography.bodyMedium,
                                modifier = Modifier.padding(horizontal = 16.dp)
                            )
                            
                            IconButton(
                                onClick = { 
                                    if (currentPage < (historyData.size + itemsPerPage - 1) / itemsPerPage) {
                                        currentPage++
                                        // 滚动到顶部
                                        scope.launch {
                                            scrollState.scrollToItem(0)
                                        }
                                    }
                                },
                                enabled = currentPage < (historyData.size + itemsPerPage - 1) / itemsPerPage
                            ) {
                                Icon(
                                    imageVector = Icons.Default.ArrowForward,
                                    contentDescription = "下一页"
                                )
                            }
                        }
                    }
                }
            }
        }
    }
}

@Composable
fun HistoryItem(result: LotteryFullResult) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surface.copy(alpha = 0.95f)
        )
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            // 期号和日期
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(bottom = 8.dp),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Text(
                    text = "${result.period}期",
                    fontWeight = FontWeight.Bold,
                    fontSize = 16.sp
                )
                Text(
                    text = result.date,
                    color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f)
                )
            }
            
            // 开奖号码 - 使用自适应布局和方块型号码
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(vertical = 8.dp),
                horizontalArrangement = Arrangement.spacedBy(4.dp)
            ) {
                // 前6个号码
                result.numbers.forEach { lotteryNumber ->
                    NumberChip(
                        number = lotteryNumber.number,
                        color = getHistoryNumberColor(lotteryNumber.number.toInt()),
                        zodiac = lotteryNumber.zodiac,
                        modifier = Modifier.weight(1f)
                    )
                }
                
                // 分隔符
                Text(
                    text = "+",
                    modifier = Modifier
                        .align(Alignment.CenterVertically)
                        .padding(horizontal = 2.dp),
                    fontSize = 18.sp,
                    fontWeight = FontWeight.Bold
                )
                
                // 特码
                NumberChip(
                    number = result.specialNumber.number,
                    color = getHistoryNumberColor(result.specialNumber.number.toInt()),
                    zodiac = result.specialNumber.zodiac,
                    isSpecial = true,
                    modifier = Modifier.weight(1f)
                )
            }
        }
    }
}

@Composable
fun NumberChip(
    number: String,
    color: Color,
    zodiac: String? = null,
    isSpecial: Boolean = false,
    modifier: Modifier = Modifier,
    onClick: () -> Unit = {}
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        modifier = modifier
    ) {
        // 方块型号码
        Surface(
            shape = androidx.compose.foundation.shape.RoundedCornerShape(8.dp),
            color = if (isSpecial) color.copy(alpha = 0.2f) else color.copy(alpha = 0.08f),
            border = androidx.compose.foundation.BorderStroke(
                width = if (isSpecial) 2.dp else 1.dp,
                color = if (isSpecial) color else color.copy(alpha = 0.3f)
            ),
            modifier = Modifier
                .fillMaxWidth()
                .aspectRatio(1f)
        ) {
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(2.dp),
                verticalArrangement = Arrangement.Center,
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                // 处理号码显示，去掉前导零
                val displayNumber = number.toIntOrNull()?.toString() ?: number
                Text(
                    text = displayNumber,
                    color = color,
                    fontSize = 14.sp,
                    fontWeight = FontWeight.Medium,
                    textAlign = TextAlign.Center,
                    modifier = Modifier.padding(bottom = 1.dp)
                )
            }
        }
        
        // 生肖
        zodiac?.let {
            Text(
                text = it,
                color = color.copy(alpha = 0.7f),
                fontSize = 12.sp,
                textAlign = TextAlign.Center,
                modifier = Modifier.padding(top = 2.dp)
            )
        }
    }
}

// 获取号码颜色
fun getHistoryNumberColor(number: Int): Color {
    return when (number) {
        in ZodiacUtils.RED_NUMBERS -> Color.Red
        in ZodiacUtils.GREEN_NUMBERS -> Color(0xFF228B22)
        else -> Color.Blue
    }
}

// 获取历史数据
suspend fun fetchHistoryData(limit: Int, onResult: (List<LotteryFullResult>) -> Unit) {
    try {
        val url = URL("https://history.macaumarksix.com/history/macaujc2/y/2025")
        val response = withContext(Dispatchers.IO) {
            url.readText()
        }
        
        // 解析JSON字符串
        val dataStart = response.indexOf("\"data\":[") + 7
        val dataEnd = response.lastIndexOf("]")
        val dataString = response.substring(dataStart, dataEnd + 1)
        
        // 解析数据数组
        val dataItems = dataString.split("},").map { it.trim().removeSurrounding("{", "}") }
        
        // 限制数据量
        val limitedItems = dataItems.take(limit)
        
        // 使用协程分批处理数据，避免UI卡顿
        val results = mutableListOf<LotteryFullResult>()
        val batchSize = 50
        
        for (i in limitedItems.indices step batchSize) {
            val batch = limitedItems.subList(i, minOf(i + batchSize, limitedItems.size))
            val batchResults = withContext(Dispatchers.Default) {
                batch.mapNotNull { item ->
                    try {
                        // 解析逻辑保持不变
                        val expect = item.substringAfter("\"expect\":\"").substringBefore("\"")
                        val openCode = item.substringAfter("\"openCode\":\"").substringBefore("\"")
                        val date = item.substringAfter("\"openTime\":\"").substringBefore("\"")
                            .substringBefore(" ") // 只取日期部分
                        
                        // 解析生肖信息
                        val zodiacInfo = item.substringAfter("\"zodiac\":\"").substringBefore("\"")
                        val zodiacList = zodiacInfo.split(",")
                        
                        // 解析完整开奖号码
                        val allNumbers = openCode.split(",")
                        val regularNumbers = allNumbers.take(6)
                        val specialNumber = allNumbers.last()
                        
                        // 解析每个号码对应的生肖
                        // 从API返回的生肖字符串中提取
                        val allZodiacs = extractZodiacsFromResponse(item)
                        
                        // 创建号码和生肖的映射
                        val numberZodiacMap = mutableMapOf<String, String>()
                        
                        // 尝试从API响应中提取每个号码对应的生肖
                        // 这里假设API返回了一个包含所有号码生肖的字段
                        // 如果没有，我们可以使用特码生肖作为备选
                        val specialZodiac = zodiacList.firstOrNull() ?: ""
                        
                        // 创建前6个号码的LotteryNumber对象
                        val lotteryNumbers = regularNumbers.mapIndexed { index, number ->
                            // 获取该号码对应的生肖，如果没有则使用空字符串
                            val zodiac = allZodiacs[number] ?: ""
                            LotteryNumber(number, zodiac)
                        }
                        
                        // 创建特码的LotteryNumber对象
                        val specialLotteryNumber = LotteryNumber(
                            specialNumber,
                            allZodiacs[specialNumber] ?: specialZodiac
                        )
                        
                        LotteryFullResult(
                            period = expect.takeLast(3),
                            numbers = lotteryNumbers,
                            specialNumber = specialLotteryNumber,
                            date = date
                        )
                    } catch (e: Exception) {
                        null
                    }
                }
            }
            
            results.addAll(batchResults)
            
            // 每处理一批数据就更新UI
            if (results.isNotEmpty()) {
                onResult(results.toList())
            }
        }
        
        // 最终结果
        onResult(results)
    } catch (e: Exception) {
        onResult(emptyList())
    }
}

// 从API响应中提取所有号码对应的生肖
fun extractZodiacsFromResponse(responseItem: String): Map<String, String> {
    val result = mutableMapOf<String, String>()
    
    try {
        // 尝试解析API返回的生肖信息
        // 这里假设API返回了一个包含所有号码生肖的字段
        // 如果没有这样的字段，我们需要使用其他方式获取生肖信息
        
        // 首先尝试获取特码生肖
        val specialZodiac = responseItem.substringAfter("\"zodiac\":\"").substringBefore("\"")
        val specialNumber = responseItem.substringAfter("\"openCode\":\"").substringBefore("\"")
            .split(",").last()
        
        // 将特码和生肖关联起来
        result[specialNumber] = specialZodiac
        
        // 尝试解析其他号码的生肖
        // 这里我们需要根据实际API返回格式进行调整
        // 如果API没有提供每个号码的生肖，我们可以尝试从其他字段中提取
        
        // 例如，如果API返回了一个包含所有生肖的字段，格式为 "allZodiacs":"鼠:01,13,25,37,49;牛:02,14,26,38;..."
        if (responseItem.contains("\"allZodiacs\":\"")) {
            val allZodiacsStr = responseItem.substringAfter("\"allZodiacs\":\"").substringBefore("\"")
            val zodiacGroups = allZodiacsStr.split(";")
            
            for (group in zodiacGroups) {
                val parts = group.split(":")
                if (parts.size == 2) {
                    val zodiac = parts[0]
                    val numbers = parts[1].split(",")
                    
                    for (number in numbers) {
                        result[number] = zodiac
                    }
                }
            }
        }
        
        // 如果没有找到所有号码的生肖，我们可以尝试从特码生肖列表中提取
        // 假设API返回了一个特码生肖列表，格式为 "zodiacList":"猪,猴,马,羊,鸡,猴,兔"
        if (result.size <= 1 && responseItem.contains("\"zodiacList\":\"")) {
            val zodiacListStr = responseItem.substringAfter("\"zodiacList\":\"").substringBefore("\"")
            val zodiacList = zodiacListStr.split(",")
            
            val openCode = responseItem.substringAfter("\"openCode\":\"").substringBefore("\"")
            val allNumbers = openCode.split(",")
            
            // 如果生肖列表长度与号码列表长度相同，则一一对应
            if (zodiacList.size == allNumbers.size) {
                for (i in allNumbers.indices) {
                    result[allNumbers[i]] = zodiacList[i]
                }
            }
        }
        
        // 如果API没有提供每个号码的生肖，我们可以使用特码生肖作为备选
        // 这里我们解析特码生肖字符串，格式为 "zodiac":"猪,猴,马,羊,鸡,猴,兔"
        if (result.size <= 1) {
            val zodiacStr = responseItem.substringAfter("\"zodiac\":\"").substringBefore("\"")
            val zodiacs = zodiacStr.split(",")
            
            // 如果有多个生肖，可能是对应多个号码
            val openCode = responseItem.substringAfter("\"openCode\":\"").substringBefore("\"")
            val allNumbers = openCode.split(",")
            
            // 如果生肖数量与号码数量相同，则一一对应
            if (zodiacs.size == allNumbers.size) {
                for (i in allNumbers.indices) {
                    result[allNumbers[i]] = zodiacs[i]
                }
            } else if (zodiacs.isNotEmpty()) {
                // 否则，所有号码都使用第一个生肖
                for (number in allNumbers) {
                    result[number] = zodiacs[0]
                }
            }
        }
    } catch (e: Exception) {
        // 解析出错，返回空映射
    }
    
    return result
} 