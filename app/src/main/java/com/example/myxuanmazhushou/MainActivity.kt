package com.example.myxuanmazhushou

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.util.Log
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.BasicTextField
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Settings
import androidx.compose.material3.AlertDialog
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.Divider
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.FilledTonalButton
import androidx.compose.material3.FilterChip
import androidx.compose.material3.FilterChipDefaults
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.LocalTextStyle
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedTextField
import androidx.compose.material3.OutlinedTextFieldDefaults
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.material3.TextField
import androidx.compose.material3.TextFieldDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.Layout
import androidx.compose.ui.layout.Placeable
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.example.myxuanmazhushou.ui.theme.MyXuanmazhushouTheme
import com.example.myxuanmazhushou.utils.ZodiacUtils
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.net.URL


// 在文件顶部添加数据类
data class LotteryResult(
    val period: String,  // 期号
    val number: String,  // 特码
    val date: String,    // 开奖日期
    val zodiac: String   // 生肖
)

// 添加数据类来管理号码和金额
data class BetNumber(
    val numbers: List<String>, // 改为 List 来支持组合
    var amount: Int = 0,
    var isComplex: Boolean = false
)

// 在文件顶部添加投注方式枚举
enum class BetType {
    SPECIAL_NUMBER,     // 特码
    TWO_IN_TWO,        // 二中二
    TWO_IN_TWO_COMPLEX,// 二中二复式
    THREE_IN_THREE,    // 三中三
    THREE_IN_THREE_COMPLEX, // 三中三复式
    TWO_IN_THREE,      // 三中二
    TWO_IN_THREE_COMPLEX,  // 三中二复式
}

// 首先在文件顶部添加一个数据类来表示购物车中的项目
data class BetCartItem(
    val betType: BetType,
    val numbers: List<String>,
    val isComplex: Boolean = false
)

// 添加检测设备类型的函数
@Composable
fun isTablet(): Boolean {
    val configuration = LocalConfiguration.current
    return configuration.screenWidthDp >= 600 // 通常平板宽度大于600dp
}


@OptIn(ExperimentalMaterial3Api::class)
class MainActivity : ComponentActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()
        
        // 加载保存的生肖映射
        ZodiacUtils.loadSavedMappings(this)
        
        setContent {
            MyXuanmazhushouTheme {
                MainScreen(this)
            }
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun MainScreen(context: Context) {
    // 添加状态控制对话框显示
    var showYearSettingDialog by remember { mutableStateOf(false) }
    var showHistoryList by remember { mutableStateOf(false) }
    var showFilterCard by remember { mutableStateOf(true) } // 控制筛选条件卡片的可见性
    
    // 检测是否为平板设备
    val isTabletDevice = isTablet()

    // 处理年份设置
    val onYearSettingClick = {
        showYearSettingDialog = true
    }

    // 处理对话框确认
    val onYearSettingConfirm = { year: Int ->
        val zodiacMap = ZodiacUtils.generateZodiacMapping(year)
        ZodiacUtils.updateZodiacMappings(context, zodiacMap)
        showYearSettingDialog = false
    }

    // 初始化生肖映射
    LaunchedEffect(Unit) {
        val savedYear = ZodiacUtils.getCurrentBaseYear(context)
        val zodiacMap = ZodiacUtils.generateZodiacMapping(savedYear)
        ZodiacUtils.updateZodiacMappings(context, zodiacMap)
        
        // 添加调试日志
        Log.d("ZodiacDebug", "Loaded Zodiac Mappings for year $savedYear: ${ZodiacUtils.getZodiacMappings()}")
    }

    val filters = listOf(
        listOf("红波", "绿波", "蓝波"),
        listOf("大", "小", "双", "单"),
        listOf("合双", "合单", "合小", "合大"),  // 修改名称
        listOf("零", "一", "二", "三", "四"),
        listOf("0", "1", "2", "3", "4", "5", "6", "7", "8", "9"),
        listOf("鼠", "牛", "虎", "兔", "龙", "蛇"),
        listOf("马", "羊", "猴", "鸡", "狗", "猪")
    )
    
    var selectedFilters by remember { mutableStateOf(setOf<String>()) }
    var selectedHeadNumbers by remember { mutableStateOf(setOf<String>()) }
    var selectedTailNumbers by remember { mutableStateOf(setOf<String>()) }
    
    // 模拟号码数据
    val allNumbers = (1..49).map { 
        if (it < 10) "0$it" else it.toString() 
    }
    
    // 根据筛选条件过滤号码
    val filteredNumbers = allNumbers.filter { number ->
        val num = number.toInt()
        val zodiac = ZodiacUtils.getZodiacForNumber(num)
        
        // 基础筛选条件
        val baseCondition = if (selectedFilters.isEmpty() && 
                                selectedHeadNumbers.isEmpty() && 
                                selectedTailNumbers.isEmpty()) {
            true
        } else {
            // 将筛选条件分组，每组内是OR关系，组之间是AND关系
            val filterGroups = selectedFilters.groupBy { filter ->
                when (filter) {
                    "红波", "绿波", "蓝波" -> "波色"
                    "大", "小" -> "大小"
                    "单", "双" -> "单双"
                    "合单", "合双" -> "合单双"
                    "合小", "合大" -> "合大小"
                    "家禽", "野兽" -> "禽兽"
                    "单肖", "双肖" -> "单双肖"
                    in ZodiacUtils.ZODIAC_ORDER -> "生肖"
                    else -> "其他"
                }
            }

            // 检查每个分组是否满足条件
            val filterCondition = filterGroups.all { (group, filters) ->
                filters.any { filter ->
                    when (filter) {
                        "红波" -> num in ZodiacUtils.RED_NUMBERS
                        "绿波" -> num in ZodiacUtils.GREEN_NUMBERS
                        "蓝波" -> num in ZodiacUtils.BLUE_NUMBERS
                        "大" -> num > 24
                        "小" -> num <= 24
                        "单" -> num % 2 != 0
                        "双" -> num % 2 == 0
                        "合单" -> getDigitSum(num) % 2 != 0
                        "合双" -> getDigitSum(num) % 2 == 0
                        "合小" -> getDigitSum(num) in 1..6
                        "合大" -> getDigitSum(num) in 7..13
                        "家禽" -> zodiac != null && zodiac in ZodiacUtils.DOMESTIC_ANIMALS
                        "野兽" -> zodiac != null && zodiac in ZodiacUtils.WILD_ANIMALS
                        "单肖" -> zodiac != null && zodiac in ZodiacUtils.EVEN_ZODIACS // 单肖使用EVEN_ZODIACS
                        "双肖" -> zodiac != null && zodiac in ZodiacUtils.ODD_ZODIACS  // 双肖使用ODD_ZODIACS
                        in ZodiacUtils.ZODIAC_ORDER -> {
                            val zodiacNumbers = ZodiacUtils.getZodiacMappings()
                            num in (zodiacNumbers[filter] ?: emptyList())
                        }
                        else -> true
                    }
                }
            }
            
            // 字头数条件
            val headCondition = if (selectedHeadNumbers.isEmpty()) true
                                else selectedHeadNumbers.any { 
                                    val headNumber = ZodiacUtils.CHINESE_NUMBERS[it] ?: 0
                                    num / 10 == headNumber 
                                }
            
            // 尾数条件
            val tailCondition = if (selectedTailNumbers.isEmpty()) true
                                else selectedTailNumbers.any { 
                                    num % 10 == it.toInt() 
                                }
            
            filterCondition && headCondition && tailCondition
        }
        
        baseCondition
    }

    // 在 MainScreen 中修改状态声明
    var historyData by remember { mutableStateOf<List<LotteryResult>>(emptyList()) }
    var isLoading by remember { mutableStateOf(false) }
    val scope = rememberCoroutineScope()

    // 修改获取历史数据的函数
    suspend fun fetchHistoryData() {
        try {
            isLoading = true
            val url = URL("https://history.macaumarksix.com/history/macaujc2/y/2025")
            val response = withContext(Dispatchers.IO) {
                url.readText()
            }
            
            // 解析JSON字符串
            val dataStart = response.indexOf("\"data\":[") + 7
            val dataEnd = response.lastIndexOf("]")
            val dataString = response.substring(dataStart, dataEnd + 1)
            
            // 解析数据数组
            val dataItems = dataString.split("},").map { it.trim().removeSurrounding("{", "}") }
            val numbers = dataItems.take(60).mapNotNull { item ->
                try {
                    val expect = item.substringAfter("\"expect\":\"").substringBefore("\"")
                    val openCode = item.substringAfter("\"openCode\":\"").substringBefore("\"")
                    val zodiac = item.substringAfter("\"zodiac\":\"").substringBefore("\"")
                    val specialNumber = openCode.split(",").last()
                    
                    LotteryResult(
                        period = expect.takeLast(3),
                        number = specialNumber,
                        date = "",
                        zodiac = zodiac
                    )
                } catch (e: Exception) {
                    Log.e("History", "Error parsing item: ${e.message}", e)
                    null
                }
            }
            historyData = numbers
            showHistoryList = true
        } catch (e: Exception) {
            Log.e("History", "Error fetching history data: ${e.message}", e)
        } finally {
            isLoading = false
        }
    }

    // 添加选中号码的状态
    var selectedNumbers by remember { mutableStateOf(setOf<String>()) }
    var showBetDialog by remember { mutableStateOf(false) }
    var betNumbers by remember { mutableStateOf(listOf<BetNumber>()) }
    
    // 在打单对话框外部添加状态
    var unifiedAmount by remember { mutableStateOf("") }
    
    // 在 MainScreen 中添加状态
    var showBetTypeDialog by remember { mutableStateOf(false) }
    var selectedBetType by remember { mutableStateOf<BetType?>(null) }

    // 添加购物车状态
    var betCart by remember { mutableStateOf<List<BetCartItem>>(emptyList()) }
    
    // 添加购物车展开状态
    var isCartExpanded by remember { mutableStateOf(false) }
    
    // 修改打单对话框的状态管理
    var isAddingToBetCart by remember { mutableStateOf(false) } // 新增：用于区分是添加到购物车还是直接打单
    
    // 修改历史记录按钮的处理逻辑
    val onHistoryClick = {
        val intent = Intent(context, HistoryActivity::class.java)
        context.startActivity(intent)
    }
    
    // 添加智能预测按钮的处理逻辑
    val onSmartPredictionClick = {
        val intent = Intent(context, SmartPredictionActivity::class.java)
        context.startActivity(intent)
    }

    // 添加预测按钮的处理逻辑
    val onPredictionClick = {
        val intent = Intent(context, PredictionActivity::class.java)
        context.startActivity(intent)
    }

    Scaffold(
        modifier = Modifier.fillMaxSize(),
        containerColor = MaterialTheme.colorScheme.background
    ) { innerPadding ->
        if (isTabletDevice) {
            // 平板模式使用水平布局
            Row(
            modifier = Modifier
                .padding(innerPadding)
                .padding(10.dp)
                .fillMaxSize()
            ) {
                // 左侧: 筛选条件区域
                Card(
                    modifier = Modifier
                        .weight(0.4f)
                        .fillMaxHeight()
                        .padding(end = 5.dp, bottom = 10.dp), // 统一底部边距
                    colors = CardDefaults.cardColors(
                        containerColor = MaterialTheme.colorScheme.surface.copy(alpha = 0.95f)
                    ),
                    elevation = CardDefaults.cardElevation(
                        defaultElevation = 3.dp
                    )
                ) {
                    Column(
                        modifier = Modifier.padding(
                            horizontal = 16.dp,
                            vertical = 12.dp
                        )
                    ) {
                        FilterHeader(
                            onFilterClick = { /* 筛选逻辑 */ },
                            onYearSettingClick = onYearSettingClick,
                            onHistoryClick = onHistoryClick,
                            onPredictionClick = onPredictionClick,
                            onSmartPredictionClick = onSmartPredictionClick
                        )
                        
                        // 筛选条件布局
                        LazyColumn(
                            verticalArrangement = Arrangement.spacedBy(12.dp),
                            contentPadding = PaddingValues(vertical = 8.dp),
                            modifier = Modifier.weight(1f)
                        ) {
                            // 第一组：波色
                            item {
                                Column(modifier = Modifier.fillMaxWidth()) {
                                    Text(
                                        text = "波色",
                                        style = MaterialTheme.typography.titleSmall,
                                        color = MaterialTheme.colorScheme.primary,
                                        modifier = Modifier.padding(bottom = 4.dp, start = 4.dp)
                                    )
                                    Row(
                                        horizontalArrangement = Arrangement.spacedBy(8.dp),
                                        modifier = Modifier.fillMaxWidth()
                                    ) {
                                        listOf("红波", "绿波", "蓝波").forEach { filter ->
                                            FilterButton(
                                                text = filter,
                                                isSelected = selectedFilters.contains(filter),
                                                onClick = { 
                                                    selectedFilters = if (selectedFilters.contains(filter)) {
                                                        selectedFilters - filter
                                                    } else {
                                                        selectedFilters + filter
                                                    }
                                                },
                                                modifier = Modifier.weight(1f)
                                            )
                                        }
                                    }
                                }
                            }
                            
                            // 第二组：大小单双
                            item {
                                Column(modifier = Modifier.fillMaxWidth()) {
                                    Text(
                                        text = "大小单双",
                                        style = MaterialTheme.typography.titleSmall,
                                        color = MaterialTheme.colorScheme.primary,
                                        modifier = Modifier.padding(bottom = 4.dp, start = 4.dp)
                                    )
                                    Row(
                                        horizontalArrangement = Arrangement.spacedBy(8.dp),
                                        modifier = Modifier.fillMaxWidth()
                                    ) {
                                        listOf("大", "小", "单", "双").forEach { filter ->
                                            FilterButton(
                                                text = filter,
                                                isSelected = selectedFilters.contains(filter),
                                                onClick = { 
                                                    selectedFilters = if (selectedFilters.contains(filter)) {
                                                        selectedFilters - filter
                                                    } else {
                                                        selectedFilters + filter
                                                    }
                                                },
                                                modifier = Modifier.weight(1f)
                                            )
                                        }
                                    }
                                }
                            }
                            
                            // 新增组：合数类型
                            item {
                                Column(modifier = Modifier.fillMaxWidth()) {
                                    Text(
                                        text = "合数类型",
                                        style = MaterialTheme.typography.titleSmall,
                                        color = MaterialTheme.colorScheme.primary,
                                        modifier = Modifier.padding(bottom = 4.dp, start = 4.dp)
                                    )
                                    Row(
                                        horizontalArrangement = Arrangement.spacedBy(8.dp),
                                        modifier = Modifier.fillMaxWidth()
                                    ) {
                                        listOf("合双", "合单", "合小", "合大").forEach { filter ->
                                            FilterButton(
                                                text = filter,
                                                isSelected = selectedFilters.contains(filter),
                                                onClick = { 
                                                    selectedFilters = if (selectedFilters.contains(filter)) {
                                                        selectedFilters - filter
                                                    } else {
                                                        selectedFilters + filter
                                                    }
                                                },
                                                modifier = Modifier.weight(1f)
                                            )
                                        }
                                    }
                                }
                            }
                            
                            // 第三组：字头
                            item {
                                Column(modifier = Modifier.fillMaxWidth()) {
                                    Text(
                                        text = "字头",
                                        style = MaterialTheme.typography.titleSmall,
                                        color = MaterialTheme.colorScheme.primary,
                                        modifier = Modifier.padding(bottom = 4.dp, start = 4.dp)
                                    )
                                    Row(
                                        horizontalArrangement = Arrangement.spacedBy(8.dp),
                                        modifier = Modifier.fillMaxWidth()
                                    ) {
                                        listOf("零", "一", "二", "三", "四").forEach { filter ->
                                            FilterButton(
                                                text = filter,
                                                isSelected = selectedHeadNumbers.contains(filter),
                                                onClick = { 
                                                    selectedHeadNumbers = if (selectedHeadNumbers.contains(filter)) {
                                                        selectedHeadNumbers - filter
                                                    } else {
                                                        selectedHeadNumbers + filter
                                                    }
                                                },
                                                modifier = Modifier.weight(1f)
                                            )
                                        }
                                    }
                                }
                            }
                            
                            // 第四组：尾数
                            item {
                                Column(modifier = Modifier.fillMaxWidth()) {
                                    Text(
                                        text = "尾数",
                                        style = MaterialTheme.typography.titleSmall,
                                        color = MaterialTheme.colorScheme.primary,
                                        modifier = Modifier.padding(bottom = 4.dp, start = 4.dp)
                                    )
                                    
                                    // 第一行尾数0-4
                                    Row(
                                        horizontalArrangement = Arrangement.spacedBy(8.dp),
                                        modifier = Modifier
                                            .fillMaxWidth()
                                            .padding(bottom = 8.dp)
                                    ) {
                                        listOf("0", "1", "2", "3", "4").forEach { filter ->
                                            FilterButton(
                                                text = filter,
                                                isSelected = selectedTailNumbers.contains(filter),
                                                onClick = { 
                                                    selectedTailNumbers = if (selectedTailNumbers.contains(filter)) {
                                                        selectedTailNumbers - filter
                                                    } else {
                                                        selectedTailNumbers + filter
                                                    }
                                                },
                                                modifier = Modifier.weight(1f)
                                            )
                                        }
                                    }
                                    
                                    // 第二行尾数5-9
                                    Row(
                                        horizontalArrangement = Arrangement.spacedBy(8.dp),
                                        modifier = Modifier.fillMaxWidth()
                                    ) {
                                        listOf("5", "6", "7", "8", "9").forEach { filter ->
                                            FilterButton(
                                                text = filter,
                                                isSelected = selectedTailNumbers.contains(filter),
                                                onClick = { 
                                                    selectedTailNumbers = if (selectedTailNumbers.contains(filter)) {
                                                        selectedTailNumbers - filter
                                                    } else {
                                                        selectedTailNumbers + filter
                                                    }
                                                },
                                                modifier = Modifier.weight(1f)
                                            )
                                        }
                                    }
                                }
                            }
                            
                            // 第五组：生肖
                            item {
                                Column(modifier = Modifier.fillMaxWidth()) {
                                    Text(
                                        text = "生肖",
                                        style = MaterialTheme.typography.titleSmall,
                                        color = MaterialTheme.colorScheme.primary,
                                        modifier = Modifier.padding(bottom = 4.dp, start = 4.dp)
                                    )
                                    
                                    // 第一行生肖
                                    Row(
                                        horizontalArrangement = Arrangement.spacedBy(8.dp),
                                        modifier = Modifier
                                            .fillMaxWidth()
                                            .padding(bottom = 8.dp)
                                    ) {
                                        listOf("鼠", "牛", "虎", "兔", "龙", "蛇").forEach { filter ->
                                            FilterButton(
                                                text = filter,
                                                isSelected = selectedFilters.contains(filter),
                                                onClick = { 
                                                    selectedFilters = if (selectedFilters.contains(filter)) {
                                                        selectedFilters - filter
                                                    } else {
                                                        selectedFilters + filter
                                                    }
                                                },
                                                modifier = Modifier.weight(1f)
                                            )
                                        }
                                    }
                                    
                                    // 第二行生肖
                                    Row(
                                        horizontalArrangement = Arrangement.spacedBy(8.dp),
                                        modifier = Modifier.fillMaxWidth()
                                    ) {
                                        listOf("马", "羊", "猴", "鸡", "狗", "猪").forEach { filter ->
                                            FilterButton(
                                                text = filter,
                                                isSelected = selectedFilters.contains(filter),
                                                onClick = { 
                                                    selectedFilters = if (selectedFilters.contains(filter)) {
                                                        selectedFilters - filter
                                                    } else {
                                                        selectedFilters + filter
                                                    }
                                                },
                                                modifier = Modifier.weight(1f)
                                            )
                                        }
                                    }
                                }
                            }
                            
                            // 在平板模式下添加禽兽和单双肖筛选组
                            // 在LazyColumn中增加两个新的item
                            item {
                                Column(modifier = Modifier.fillMaxWidth()) {
                                    Text(
                                        text = "家野单双肖",
                                        style = MaterialTheme.typography.titleSmall,
                                        color = MaterialTheme.colorScheme.primary,
                                        modifier = Modifier.padding(bottom = 4.dp, start = 4.dp)
                                    )
                                    Row(
                                        horizontalArrangement = Arrangement.spacedBy(8.dp),
                                        modifier = Modifier.fillMaxWidth()
                                    ) {
                                        listOf("家禽", "野兽", "单肖", "双肖").forEach { filter ->
                                            FilterButton(
                                                text = filter,
                                                isSelected = selectedFilters.contains(filter),
                                                onClick = { 
                                                    selectedFilters = if (selectedFilters.contains(filter)) {
                                                        selectedFilters - filter
                                                    } else {
                                                        selectedFilters + filter
                                                    }
                                                },
                                                modifier = Modifier.weight(1f)
                                            )
                                        }
                                    }
                                }
                            }
                        }
                        
                        // 把已选条件移到筛选条件下方
                        if (selectedFilters.isNotEmpty() || selectedHeadNumbers.isNotEmpty() || selectedTailNumbers.isNotEmpty()) {
                            Column(
                                modifier = Modifier.padding(vertical = 8.dp)
                            ) {
                                Text(
                                    text = "已选条件:",
                                    style = MaterialTheme.typography.titleSmall,
                                    color = MaterialTheme.colorScheme.primary,
                                    modifier = Modifier.padding(bottom = 4.dp)
                                )
                                
                                LazyRow(
                                    horizontalArrangement = Arrangement.spacedBy(4.dp),
                                    contentPadding = PaddingValues(vertical = 4.dp),
                                    modifier = Modifier.fillMaxWidth()
                                ) {
                                    items(selectedFilters.toList()) { filter ->
                                        FilterChip(
                                            selected = true,
                                            onClick = { 
                                                selectedFilters = selectedFilters - filter
                                            },
                                            label = { Text(filter) },
                                            colors = FilterChipDefaults.filterChipColors(
                                                selectedContainerColor = MaterialTheme.colorScheme.primaryContainer,
                                                containerColor = MaterialTheme.colorScheme.surfaceVariant
                                            )
                                        )
                                    }
                                    
                                    if (selectedHeadNumbers.isNotEmpty()) {
                                        item {
                                            FilterChip(
                                                selected = true,
                                                onClick = { selectedHeadNumbers = emptySet() },
                                                label = { Text("字头: ${selectedHeadNumbers.joinToString(",")}") },
                                                colors = FilterChipDefaults.filterChipColors(
                                                    selectedContainerColor = MaterialTheme.colorScheme.primaryContainer,
                                                    containerColor = MaterialTheme.colorScheme.surfaceVariant
                                                )
                                            )
                                        }
                                    }
                                    
                                    if (selectedTailNumbers.isNotEmpty()) {
                                        item {
                                            FilterChip(
                                                selected = true,
                                                onClick = { selectedTailNumbers = emptySet() },
                                                label = { Text("尾数: ${selectedTailNumbers.joinToString(",")}") },
                                                colors = FilterChipDefaults.filterChipColors(
                                                    selectedContainerColor = MaterialTheme.colorScheme.primaryContainer,
                                                    containerColor = MaterialTheme.colorScheme.surfaceVariant
                                                )
                                            )
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
                
                // 右侧: 号码显示区域和操作区域
                Column(
                    modifier = Modifier
                        .weight(0.6f)
                        .fillMaxHeight()
        ) {
            // 号码显示区域
            Card(
                modifier = Modifier
                    .weight(1f)
                    .padding(bottom = 10.dp),
                colors = CardDefaults.cardColors(
                    containerColor = MaterialTheme.colorScheme.surface.copy(alpha = 0.95f)
                ),
                elevation = CardDefaults.cardElevation(
                    defaultElevation = 2.dp
                )
            ) {
                Column(modifier = Modifier.padding(16.dp)) {
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(bottom = 12.dp),
                        horizontalArrangement = Arrangement.SpaceBetween,
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Text(
                            text = "符合条件的号码 (${filteredNumbers.size}个)",
                            style = MaterialTheme.typography.titleMedium,
                            color = MaterialTheme.colorScheme.primary
                        )
                        
                        // 添加全选按钮
                        if (filteredNumbers.isNotEmpty()) {
                            TextButton(
                                onClick = {
                                    selectedNumbers = if (selectedNumbers.size == filteredNumbers.size) {
                                        emptySet() // 如果已经全选，则清空选择
                                    } else {
                                        filteredNumbers.toSet() // 否则全选
                                    }
                                }
                            ) {
                                Text(
                                    text = if (selectedNumbers.size == filteredNumbers.size) "取消全选" else "全选",
                                    color = MaterialTheme.colorScheme.primary
                                )
                            }
                        }
                    }
                    
                    if (filteredNumbers.isEmpty()) {
                        Box(
                            modifier = Modifier
                                .fillMaxSize()
                                .padding(16.dp),
                            contentAlignment = Alignment.Center
                        ) {
                            Text(
                                text = "没有符合条件的号码",
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                        }
                    } else {
                                // 移除固定高度计算，使用可滚动的LazyVerticalGrid
                                LazyVerticalGrid(
                                    columns = GridCells.Fixed(7),
                                    verticalArrangement = Arrangement.spacedBy(8.dp),  // 增加垂直间距
                                    horizontalArrangement = Arrangement.spacedBy(8.dp),  // 增加水平间距
                                    contentPadding = PaddingValues(8.dp),  // 增加内边距
                                    modifier = Modifier
                                        .fillMaxWidth()
                                        .fillMaxHeight() // 填充全部高度
                                ) {
                                    items(filteredNumbers) { number ->
                                        val zodiac = ZodiacUtils.getZodiacForNumber(number.toInt())
                                        Box(
                                            modifier = Modifier
                                                .fillMaxWidth()
                                                .height(56.dp)  // 增加高度
                                                .aspectRatio(1f)
                                        ) {
                                            NumberChip(
                                                number = number,
                                                color = when (number.toInt()) {
                                                    in ZodiacUtils.RED_NUMBERS -> Color.Red
                                                    in ZodiacUtils.GREEN_NUMBERS -> Color(0xFF228B22)
                                                    else -> Color.Blue
                                                },
                                                zodiac = zodiac,
                                                isSelected = selectedNumbers.contains(number),
                                                onClick = {
                                                    selectedNumbers = if (selectedNumbers.contains(number)) {
                                                        selectedNumbers - number
                                                    } else {
                                                        selectedNumbers + number
                                                    }
                                                }
                                            )
                                        }
                                    }
                                }
                            }
                        }
                    }
                    
                    // 选中号码的操作按钮区域
                    if (selectedNumbers.isNotEmpty()) {
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(vertical = 8.dp),
                            horizontalArrangement = Arrangement.SpaceBetween,
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Text(
                                text = "已选: ${selectedNumbers.size}个",
                                style = MaterialTheme.typography.bodyMedium,
                                modifier = Modifier.padding(start = 16.dp)
                            )
                            Row(
                                horizontalArrangement = Arrangement.spacedBy(8.dp),
                                modifier = Modifier.padding(end = 16.dp)
                            ) {
                                // 添加到购物车按钮
                                Button(
                                    onClick = { 
                                        isAddingToBetCart = true
                                        showBetTypeDialog = true
                                    },
                                    colors = ButtonDefaults.buttonColors(
                                        containerColor = MaterialTheme.colorScheme.secondary
                                    )
                                ) {
                                    Text("添加")
                                }
                                
                                // 直接打单按钮
                                Button(
                                    onClick = { 
                                        isAddingToBetCart = false
                                        showBetTypeDialog = true
                                    }
                                ) {
                                    Text("打单")
                                }
                            }
                        }
                    }
                    
                    // 修改购物车区域，添加标题和内容
                    if (betCart.isNotEmpty()) {
                        Card(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(horizontal = 16.dp, vertical = 8.dp),
                            colors = CardDefaults.cardColors(
                                containerColor = Color(0xFFF8F8F8)
                            ),
                            shape = RoundedCornerShape(12.dp),
                            elevation = CardDefaults.cardElevation(defaultElevation = 0.5.dp)
                        ) {
                            Column(
                                modifier = Modifier.padding(horizontal = 12.dp, vertical = 8.dp)
                            ) {
                                // 标题栏
                                Row(
                                    modifier = Modifier
                                        .fillMaxWidth()
                                        .padding(vertical = 8.dp),
                                    horizontalArrangement = Arrangement.SpaceBetween,
                                    verticalAlignment = Alignment.CenterVertically
                                ) {
                                    // 左侧标题和数量
                                    Row(
                                        verticalAlignment = Alignment.CenterVertically,
                                        horizontalArrangement = Arrangement.spacedBy(8.dp)
                                    ) {
                                        Text(
                                            text = "号码购物车",
                                            style = MaterialTheme.typography.titleMedium,
                                            color = Color(0xFF8B5CF6), // 使用紫色
                                            fontWeight = FontWeight.Medium
                                        )
                                        
                                        // 数量指示器
                                        Surface(
                                            shape = RoundedCornerShape(12.dp),
                                            color = Color(0xFFEDE9FE), // 浅紫色背景
                                            modifier = Modifier.height(22.dp)
                                        ) {
                                            Box(
                                                contentAlignment = Alignment.Center,
                                                modifier = Modifier.padding(horizontal = 8.dp)
                                            ) {
                                                Text(
                                                    text = "${betCart.size}",
                                                    color = Color(0xFF8B5CF6), // 紫色文字
                                                    fontWeight = FontWeight.Medium,
                                                    fontSize = 12.sp
                                                )
                                            }
                                        }
                                    }
                                    
                                    // 右侧操作按钮
                                    Row(
                                        horizontalArrangement = Arrangement.spacedBy(8.dp),
                                        verticalAlignment = Alignment.CenterVertically
                                    ) {
                                        // 清空按钮
                                        TextButton(
                                            onClick = { betCart = emptyList() },
                                            colors = ButtonDefaults.textButtonColors(
                                                contentColor = Color(0xFFEF4444) // 红色
                                            )
                                        ) {
                                            Text("清空")
                                        }
                                        
                                        // 打单按钮
                                        Button(
                                            onClick = {
                                                // 使用购物车内容创建打单
                                                betNumbers = betCart.flatMap { item ->
                                                    when (item.betType) {
                                                        BetType.SPECIAL_NUMBER -> 
                                                            item.numbers.map { BetNumber(listOf(it)) }
                                                        BetType.TWO_IN_TWO, BetType.TWO_IN_TWO_COMPLEX ->
                                                            if (item.isComplex) {
                                                                item.numbers.combinations(2).map { 
                                                                    BetNumber(it, isComplex = true) 
                                                                }
                                                            } else {
                                                                item.numbers.sequentialPairs().map { 
                                                                    BetNumber(it) 
                                                                }
                                                            }
                                                        BetType.THREE_IN_THREE, BetType.THREE_IN_THREE_COMPLEX,
                                                        BetType.TWO_IN_THREE, BetType.TWO_IN_THREE_COMPLEX ->
                                                            if (item.isComplex) {
                                                                item.numbers.combinations(3).map { 
                                                                    BetNumber(it, isComplex = true) 
                                                                }
                                                            } else {
                                                                item.numbers.sequentialTriples().map { 
                                                                    BetNumber(it) 
                                                                }
                                                            }
                                                    }
                                                }
                                                selectedBetType = null
                                                showBetDialog = true
                                            }
                                        ) {
                                            Text("打单")
                                        }
                                    }
                                }
                                
                                // 购物车内容摘要
                                Text(
                                    text = "已添加号码: ${betCart.sumOf { it.numbers.size }}个",
                                    style = MaterialTheme.typography.bodyMedium,
                                    color = MaterialTheme.colorScheme.onSurfaceVariant,
                                    modifier = Modifier.padding(vertical = 4.dp)
                                )
                                
                                // 添加号码预览
                                if (betCart.isNotEmpty()) {
                                    Divider(
                                        color = MaterialTheme.colorScheme.outline.copy(alpha = 0.3f),
                                        modifier = Modifier.padding(vertical = 4.dp)
                                    )
                                    
                                    LazyRow(
                                        horizontalArrangement = Arrangement.spacedBy(8.dp),
                                        contentPadding = PaddingValues(vertical = 8.dp),
                                        modifier = Modifier
                                            .fillMaxWidth()
                                            .heightIn(max = 88.dp)
                                    ) {
                                        items(betCart) { item ->
                                            Card(
                                                shape = RoundedCornerShape(8.dp),
                                                border = BorderStroke(1.dp, MaterialTheme.colorScheme.outline.copy(alpha = 0.3f)),
                                                colors = CardDefaults.cardColors(
                                                    containerColor = MaterialTheme.colorScheme.surfaceVariant.copy(alpha = 0.3f)
                                                ),
                                                modifier = Modifier.width(100.dp)
                                            ) {
                                                Column(
                                                    modifier = Modifier.padding(8.dp),
                                                    horizontalAlignment = Alignment.CenterHorizontally
                                                ) {
                                                    // 显示投注类型
                                                    Text(
                                                        text = when (item.betType) {
                                                            BetType.SPECIAL_NUMBER -> "特码"
                                                            BetType.TWO_IN_TWO -> "二中二"
                                                            BetType.TWO_IN_TWO_COMPLEX -> "二中二(复)"
                                                            BetType.THREE_IN_THREE -> "三中三"
                                                            BetType.THREE_IN_THREE_COMPLEX -> "三中三(复)"
                                                            BetType.TWO_IN_THREE -> "三中二"
                                                            BetType.TWO_IN_THREE_COMPLEX -> "三中二(复)"
                                                        },
                                                        fontSize = 12.sp,
                                                        fontWeight = FontWeight.Bold,
                                                        color = MaterialTheme.colorScheme.primary
                                                    )
                                                    
                                                    // 显示号码
                                                    Box(
                                                        modifier = Modifier
                                                            .padding(vertical = 4.dp)
                                                            .fillMaxWidth(),
                                                        contentAlignment = Alignment.Center
                                                    ) {
                                                        // 只显示前几个号码，如果太多则显示省略号
                                                        val displayNumbers = if (item.numbers.size > 3) 
                                                            item.numbers.take(3) + "..." 
                                                        else 
                                                            item.numbers
                                                        
                                                        Text(
                                                            text = displayNumbers.joinToString(" "),
                                                            fontSize = 12.sp,
                                                            color = MaterialTheme.colorScheme.onSurfaceVariant,
                                                            maxLines = 1,
                                                            overflow = TextOverflow.Ellipsis
                                                        )
                                                    }
                                                    
                                                    // 显示数量
                                                    Text(
                                                        text = "${item.numbers.size}个",
                                                        fontSize = 11.sp,
                                                        color = Color(0xFF8B5CF6)
                                                    )
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        } else {
            // 手机模式使用垂直布局
            Column(
                modifier = Modifier
                    .padding(innerPadding)
                    .padding(10.dp)
                    .fillMaxSize()
            ) {
                // 号码显示区域
                Card(
                    modifier = Modifier
                        .weight(1f)
                        .padding(bottom = 10.dp),
                    colors = CardDefaults.cardColors(
                        containerColor = MaterialTheme.colorScheme.surface.copy(alpha = 0.95f)
                    ),
                    elevation = CardDefaults.cardElevation(
                        defaultElevation = 2.dp
                    )
                ) {
                    Column(modifier = Modifier.padding(16.dp)) {
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(bottom = 12.dp),
                            horizontalArrangement = Arrangement.SpaceBetween,
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Text(
                                text = "符合条件的号码 (${filteredNumbers.size}个)",
                                style = MaterialTheme.typography.titleMedium,
                                color = MaterialTheme.colorScheme.primary
                            )
                            
                            // 添加全选按钮
                            if (filteredNumbers.isNotEmpty()) {
                                TextButton(
                                    onClick = {
                                        selectedNumbers = if (selectedNumbers.size == filteredNumbers.size) {
                                            emptySet() // 如果已经全选，则清空选择
                                        } else {
                                            filteredNumbers.toSet() // 否则全选
                                        }
                                    }
                                ) {
                                    Text(
                                        text = if (selectedNumbers.size == filteredNumbers.size) "取消全选" else "全选",
                                        color = MaterialTheme.colorScheme.primary
                                    )
                                }
                            }
                        }
                        
                        if (filteredNumbers.isEmpty()) {
                            Box(
                                modifier = Modifier
                                    .fillMaxSize()
                                    .padding(16.dp),
                                contentAlignment = Alignment.Center
                            ) {
                                Text(
                                    text = "没有符合条件的号码",
                                    color = MaterialTheme.colorScheme.onSurfaceVariant
                                )
                            }
                        } else {
                            // 移除固定高度计算，使用可滚动的LazyVerticalGrid
                        LazyVerticalGrid(
                            columns = GridCells.Fixed(7),
                                verticalArrangement = Arrangement.spacedBy(8.dp),  // 增加垂直间距
                                horizontalArrangement = Arrangement.spacedBy(8.dp),  // 增加水平间距
                                contentPadding = PaddingValues(8.dp),  // 增加内边距
                            modifier = Modifier
                                .fillMaxWidth()
                                .fillMaxHeight()
                                    // .heightIn(min = 150.dp, max = 400.dp)
                        ) {
                            items(filteredNumbers) { number ->
                                val zodiac = ZodiacUtils.getZodiacForNumber(number.toInt()) // 生肖
                                Box(
                                    modifier = Modifier
                                        .fillMaxWidth()
                                            .height(40.dp)  // 号码的上下距离
                                        .aspectRatio(1f)
                                ) {
                                    NumberChip(
                                        number = number,
                                        color = when (number.toInt()) {
                                            in ZodiacUtils.RED_NUMBERS -> Color.Red
                                            in ZodiacUtils.GREEN_NUMBERS -> Color(0xFF228B22)
                                            else -> Color.Blue
                                        },
                                        zodiac = zodiac,
                                        isSelected = selectedNumbers.contains(number),
                                        onClick = {
                                            selectedNumbers = if (selectedNumbers.contains(number)) {
                                                selectedNumbers - number
                                            } else {
                                                selectedNumbers + number
                                            }
                                        }
                                    )
                                }
                            }
                        }
                    }
                }
            }
            
            // 如果正在加载，显示加载指示器
            if (isLoading) {
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(vertical = 8.dp),
                    contentAlignment = Alignment.Center
                ) {
                    CircularProgressIndicator()
                }
            }
            
            // 历史记录卡片 - 只保留这一处显示
            if (showHistoryList && !isLoading && historyData.isNotEmpty()) {
                Card(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(vertical = 8.dp),
                    colors = CardDefaults.cardColors(
                        containerColor = MaterialTheme.colorScheme.surface.copy(alpha = 0.95f)
                    )
                ) {
                    Column(
                        modifier = Modifier.padding(8.dp)
                    ) {
                        Text(
                            text = "最近开奖记录",
                            style = MaterialTheme.typography.titleSmall,
                            color = MaterialTheme.colorScheme.primary,
                            modifier = Modifier.padding(bottom = 8.dp, start = 8.dp)//增加底部填充
                        )
                        
                        LazyRow(
                            contentPadding = PaddingValues(4.dp),//增加左右填充
                            horizontalArrangement = Arrangement.spacedBy(4.dp)
                        ) {
                            items(historyData) { result ->
                                Column(
                                    modifier = Modifier.width(60.dp),//增加宽度
                                    horizontalAlignment = Alignment.CenterHorizontally
                                ) {
                                    val num = result.number.toIntOrNull() ?: 0
                                    val zodiac = ZodiacUtils.getZodiacForNumber(num)
                                    Box(
                                        modifier = Modifier
                                            .size(56.dp)//增加大小
                                            .padding(bottom = 4.dp)
                                    ) {
                                        NumberChip(
                                            number = result.number,
                                            color = when (num) {
                                                in ZodiacUtils.RED_NUMBERS -> Color.Red
                                                in ZodiacUtils.GREEN_NUMBERS -> Color(0xFF228B22)
                                                else -> Color.Blue
                                            },
                                            zodiac = zodiac
                                        )
                                    }
                                    Text(
                                        text = "${result.period}期",
                                        fontSize = 12.sp,
                                        color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
                                    )
                                }
                            }
                        }
                    }
                }
            }
            
                // 筛选条件区域 - 手机模式下也使用更清晰的布局
            AnimatedVisibility(visible = showFilterCard) {
                Card(
                    modifier = Modifier.fillMaxWidth(),
                    colors = CardDefaults.cardColors(
                        containerColor = MaterialTheme.colorScheme.surface.copy(alpha = 0.95f)
                    ),
                    elevation = CardDefaults.cardElevation(
                        defaultElevation = 2.dp
                    )
                ) {
                    Column(
                        modifier = Modifier.padding(
                            horizontal = 16.dp,
                            vertical = 12.dp
                        )
                    ) {
                            FilterHeader(
                                onFilterClick = { /* 筛选逻辑 */ },
                                onYearSettingClick = onYearSettingClick,
                                onHistoryClick = onHistoryClick,
                                onPredictionClick = onPredictionClick,
                                onSmartPredictionClick = onSmartPredictionClick
                            )
                            
                            // 使用和平板模式一样的筛选条件分组
                            LazyColumn(
                                verticalArrangement = Arrangement.spacedBy(12.dp),
                                contentPadding = PaddingValues(vertical = 8.dp)
                            ) {
                                // 第一组：波色
                                item {
                                    Column(modifier = Modifier.fillMaxWidth()) {
                                Text(
                                            text = "波色",
                                    style = MaterialTheme.typography.titleSmall,
                                    color = MaterialTheme.colorScheme.primary,
                                            modifier = Modifier.padding(bottom = 4.dp, start = 4.dp)
                                        )
                                        Row(
                                            horizontalArrangement = Arrangement.spacedBy(8.dp),
                                    modifier = Modifier.fillMaxWidth()
                                ) {
                                            listOf("红波", "绿波", "蓝波").forEach { filter ->
                                                FilterButton(
                                                    text = filter,
                                                    isSelected = selectedFilters.contains(filter),
                                            onClick = { 
                                                        selectedFilters = if (selectedFilters.contains(filter)) {
                                                            selectedFilters - filter
                                                        } else {
                                                            selectedFilters + filter
                                                        }
                                                    },
                                                    modifier = Modifier.weight(1f)
                                                )
                                            }
                                        }
                                    }
                                }
                                
                                // 第二组：大小单双
                                        item {
                                    Column(modifier = Modifier.fillMaxWidth()) {
                                        Text(
                                            text = "大小单双",
                                            style = MaterialTheme.typography.titleSmall,
                                            color = MaterialTheme.colorScheme.primary,
                                            modifier = Modifier.padding(bottom = 4.dp, start = 4.dp)
                                        )
                                        Row(
                                            horizontalArrangement = Arrangement.spacedBy(8.dp),
                                            modifier = Modifier.fillMaxWidth()
                                        ) {
                                            listOf("大", "小", "单", "双").forEach { filter ->
                                                FilterButton(
                                                    text = filter,
                                                    isSelected = selectedFilters.contains(filter),
                                                    onClick = { 
                                                        selectedFilters = if (selectedFilters.contains(filter)) {
                                                            selectedFilters - filter
                                                        } else {
                                                            selectedFilters + filter
                                                        }
                                                    },
                                                    modifier = Modifier.weight(1f)
                                                )
                                            }
                                        }
                                    }
                                }
                                
                                // 新增组：合数类型
                                        item {
                                    Column(modifier = Modifier.fillMaxWidth()) {
                                        Text(
                                            text = "合数类型",
                                            style = MaterialTheme.typography.titleSmall,
                                            color = MaterialTheme.colorScheme.primary,
                                            modifier = Modifier.padding(bottom = 4.dp, start = 4.dp)
                                        )
                                        Row(
                                            horizontalArrangement = Arrangement.spacedBy(8.dp),
                                            modifier = Modifier.fillMaxWidth()
                                        ) {
                                            listOf("合双", "合单", "合小", "合大").forEach { filter ->
                                                FilterButton(
                                                    text = filter,
                                                    isSelected = selectedFilters.contains(filter),
                                                    onClick = { 
                                                        selectedFilters = if (selectedFilters.contains(filter)) {
                                                            selectedFilters - filter
                                                        } else {
                                                            selectedFilters + filter
                                                        }
                                                    },
                                                    modifier = Modifier.weight(1f)
                                                )
                                            }
                                        }
                                    }
                                }
                                
                                // 第三组：字头
                                item {
                                    Column(modifier = Modifier.fillMaxWidth()) {
                                        Text(
                                            text = "字头",
                                            style = MaterialTheme.typography.titleSmall,
                                            color = MaterialTheme.colorScheme.primary,
                                            modifier = Modifier.padding(bottom = 4.dp, start = 4.dp)
                                        )
                                Row(
                                    horizontalArrangement = Arrangement.spacedBy(8.dp),
                                    modifier = Modifier.fillMaxWidth()
                                ) {
                                            listOf("零", "一", "二", "三", "四").forEach { filter ->
                                        FilterButton(
                                            text = filter,
                                                    isSelected = selectedHeadNumbers.contains(filter),
                                            onClick = { 
                                                        selectedHeadNumbers = if (selectedHeadNumbers.contains(filter)) {
                                                            selectedHeadNumbers - filter
                                                        } else {
                                                            selectedHeadNumbers + filter
                                                        }
                                                    },
                                                    modifier = Modifier.weight(1f)
                                                )
                                            }
                                        }
                                    }
                                }
                                
                                // 第四组：尾数
                                item {
                                    Column(modifier = Modifier.fillMaxWidth()) {
                                        Text(
                                            text = "尾数",
                                            style = MaterialTheme.typography.titleSmall,
                                            color = MaterialTheme.colorScheme.primary,
                                            modifier = Modifier.padding(bottom = 4.dp, start = 4.dp)
                                        )
                                        
                                        // 第一行尾数0-4
                                        Row(
                                            horizontalArrangement = Arrangement.spacedBy(8.dp),
                                            modifier = Modifier
                                                .fillMaxWidth()
                                                .padding(bottom = 8.dp)
                                        ) {
                                            listOf("0", "1", "2", "3", "4").forEach { filter ->
                                                FilterButton(
                                                    text = filter,
                                                    isSelected = selectedTailNumbers.contains(filter),
                                                    onClick = { 
                                                        selectedTailNumbers = if (selectedTailNumbers.contains(filter)) {
                                                            selectedTailNumbers - filter
                                                        } else {
                                                            selectedTailNumbers + filter
                                                        }
                                                    },
                                                    modifier = Modifier.weight(1f)
                                                )
                                            }
                                        }
                                        
                                        // 第二行尾数5-9
                                        Row(
                                            horizontalArrangement = Arrangement.spacedBy(8.dp),
                                            modifier = Modifier.fillMaxWidth()
                                        ) {
                                            listOf("5", "6", "7", "8", "9").forEach { filter ->
                                                FilterButton(
                                                    text = filter,
                                                    isSelected = selectedTailNumbers.contains(filter),
                                                    onClick = { 
                                                        selectedTailNumbers = if (selectedTailNumbers.contains(filter)) {
                                                            selectedTailNumbers - filter
                                                        } else {
                                                            selectedTailNumbers + filter
                                                        }
                                                    },
                                                    modifier = Modifier.weight(1f)
                                                )
                                            }
                                        }
                                    }
                                }
                                
                                // 第五组：生肖
                                item {
                                    Column(modifier = Modifier.fillMaxWidth()) {
                                        Text(
                                            text = "生肖",
                                            style = MaterialTheme.typography.titleSmall,
                                            color = MaterialTheme.colorScheme.primary,
                                            modifier = Modifier.padding(bottom = 4.dp, start = 4.dp)
                                        )
                                        
                                        // 第一行生肖
                                        Row(
                                            horizontalArrangement = Arrangement.spacedBy(8.dp),
                                            modifier = Modifier
                                                .fillMaxWidth()
                                                .padding(bottom = 8.dp)
                                        ) {
                                            listOf("鼠", "牛", "虎", "兔", "龙", "蛇").forEach { filter ->
                                                FilterButton(
                                                    text = filter,
                                                    isSelected = selectedFilters.contains(filter),
                                                    onClick = { 
                                                        selectedFilters = if (selectedFilters.contains(filter)) {
                                                            selectedFilters - filter
                                                        } else {
                                                            selectedFilters + filter
                                                        }
                                                    },
                                                    modifier = Modifier.weight(1f)
                                                )
                                            }
                                        }
                                        
                                        // 第二行生肖
                                        Row(
                                            horizontalArrangement = Arrangement.spacedBy(8.dp),
                                            modifier = Modifier.fillMaxWidth()
                                        ) {
                                            listOf("马", "羊", "猴", "鸡", "狗", "猪").forEach { filter ->
                                                FilterButton(
                                                    text = filter,
                                                    isSelected = selectedFilters.contains(filter),
                                                    onClick = { 
                                                        selectedFilters = if (selectedFilters.contains(filter)) {
                                                            selectedFilters - filter
                                                        } else {
                                                            selectedFilters + filter
                                                }
                                            },
                                            modifier = Modifier.weight(1f)
                                        )
                                    }
                                }
                            }
                        }
                                
                                // 在手机模式下同样添加禽兽和单双肖筛选组
                                // 在另一个LazyColumn中同样增加两个新的item
                                item {
                                    Column(modifier = Modifier.fillMaxWidth()) {
                                        Text(
                                            text = "家野单双肖",
                                            style = MaterialTheme.typography.titleSmall,
                                            color = MaterialTheme.colorScheme.primary,
                                            modifier = Modifier.padding(bottom = 4.dp, start = 4.dp)
                                        )
                                        Row(
                                            horizontalArrangement = Arrangement.spacedBy(8.dp),
                                            modifier = Modifier.fillMaxWidth()
                                        ) {
                                            listOf("家禽", "野兽", "单肖", "双肖").forEach { filter ->
                                                FilterButton(
                                                    text = filter,
                                                    isSelected = selectedFilters.contains(filter),
                                                    onClick = { 
                                                        selectedFilters = if (selectedFilters.contains(filter)) {
                                                            selectedFilters - filter
                                                        } else {
                                                            selectedFilters + filter
                                                        }
                                                    },
                                                    modifier = Modifier.weight(1f)
                                                )
                                            }
                                        }
                                    }
                                }
                            }
                            
                            // 把已选条件移到筛选条件下方
                            if (selectedFilters.isNotEmpty() || selectedHeadNumbers.isNotEmpty() || selectedTailNumbers.isNotEmpty()) {
                                Column(
                                    modifier = Modifier.padding(vertical = 8.dp)
                                ) {
                                    Text(
                                        text = "已选条件:",
                                        style = MaterialTheme.typography.titleSmall,
                                        color = MaterialTheme.colorScheme.primary,
                                        modifier = Modifier.padding(bottom = 4.dp)
                                    )
                                    
                                    LazyRow(
                                        horizontalArrangement = Arrangement.spacedBy(4.dp),
                                        contentPadding = PaddingValues(vertical = 4.dp),
                                        modifier = Modifier.fillMaxWidth()
                                    ) {
                                        items(selectedFilters.toList()) { filter ->
                                            FilterChip(
                                                selected = true,
                                                onClick = { 
                                                    selectedFilters = selectedFilters - filter
                                                },
                                                label = { Text(filter) },
                                                colors = FilterChipDefaults.filterChipColors(
                                                    selectedContainerColor = MaterialTheme.colorScheme.primaryContainer,
                                                    containerColor = MaterialTheme.colorScheme.surfaceVariant
                                                )
                                            )
                                        }
                                        
                                        if (selectedHeadNumbers.isNotEmpty()) {
                                            item {
                                                FilterChip(
                                                    selected = true,
                                                    onClick = { selectedHeadNumbers = emptySet() },
                                                    label = { Text("字头: ${selectedHeadNumbers.joinToString(",")}") },
                                                    colors = FilterChipDefaults.filterChipColors(
                                                        selectedContainerColor = MaterialTheme.colorScheme.primaryContainer,
                                                        containerColor = MaterialTheme.colorScheme.surfaceVariant
                                                    )
                                                )
                                            }
                                        }
                                        
                                        if (selectedTailNumbers.isNotEmpty()) {
                                            item {
                                                FilterChip(
                                                    selected = true,
                                                    onClick = { selectedTailNumbers = emptySet() },
                                                    label = { Text("尾数: ${selectedTailNumbers.joinToString(",")}") },
                                                    colors = FilterChipDefaults.filterChipColors(
                                                        selectedContainerColor = MaterialTheme.colorScheme.primaryContainer,
                                                        containerColor = MaterialTheme.colorScheme.surfaceVariant
                                                    )
                                                )
                                            }
                                        }
                                    }
                                }
                            }
                    }
                }
            }
            
            // 筛选条件折叠按钮
            TextButton(
                onClick = { showFilterCard = !showFilterCard },
                modifier = Modifier.align(Alignment.CenterHorizontally)
            ) {
                Text(if (showFilterCard) "收起筛选条件" else "展开筛选条件")
            }

            // 修改选中号码的操作按钮区域
            if (selectedNumbers.isNotEmpty()) {
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(vertical = 8.dp),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = "已选: ${selectedNumbers.size}个",
                        style = MaterialTheme.typography.bodyMedium,
                        modifier = Modifier.padding(start = 16.dp)
                    )
                    Row(
                        horizontalArrangement = Arrangement.spacedBy(8.dp),
                        modifier = Modifier.padding(end = 16.dp)
                    ) {
                        // 添加到购物车按钮
                        Button(
                            onClick = { 
                                isAddingToBetCart = true
                                showBetTypeDialog = true
                            },
                            colors = ButtonDefaults.buttonColors(
                                containerColor = MaterialTheme.colorScheme.secondary
                            )
                        ) {
                            Text("添加")
                        }
                        
                        // 直接打单按钮
                        Button(
                            onClick = { 
                                isAddingToBetCart = false
                                showBetTypeDialog = true
                            }
                        ) {
                            Text("打单")
                        }
                    }
                }
            }
            
                // 显示购物车内容（手机模式）
            if (betCart.isNotEmpty()) {
                Card(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = 16.dp, vertical = 8.dp),
                    colors = CardDefaults.cardColors(
                            containerColor = Color(0xFFF8F8F8)
                    ),
                    shape = RoundedCornerShape(12.dp),
                    elevation = CardDefaults.cardElevation(defaultElevation = 0.5.dp)
                ) {
                    Column(
                        modifier = Modifier.padding(horizontal = 12.dp, vertical = 8.dp)
                    ) {
                        // 标题栏
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(vertical = 8.dp),
                            horizontalArrangement = Arrangement.SpaceBetween,
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            // 左侧标题和数量
                            Row(
                                verticalAlignment = Alignment.CenterVertically,
                                horizontalArrangement = Arrangement.spacedBy(8.dp)
                            ) {
                                Text(
                                        text = "号码购物车",
                                    style = MaterialTheme.typography.titleMedium,
                                        color = Color(0xFF8B5CF6), // 使用紫色
                                    fontWeight = FontWeight.Medium
                                )
                                
                                // 数量指示器
                                Surface(
                                    shape = RoundedCornerShape(12.dp),
                                    color = Color(0xFFEDE9FE), // 浅紫色背景
                                    modifier = Modifier.height(22.dp)
                                ) {
                                    Box(
                                        contentAlignment = Alignment.Center,
                                        modifier = Modifier.padding(horizontal = 8.dp)
                                    ) {
                                        Text(
                                            text = "${betCart.size}",
                                            color = Color(0xFF8B5CF6), // 紫色文字
                                            fontWeight = FontWeight.Medium,
                                            fontSize = 12.sp
                                        )
                                    }
                                }
                            }
                            
                            // 右侧操作按钮
                            Row(
                                horizontalArrangement = Arrangement.spacedBy(8.dp),
                                verticalAlignment = Alignment.CenterVertically
                            ) {
                                // 清空按钮
                                TextButton(
                                    onClick = { betCart = emptyList() },
                                    colors = ButtonDefaults.textButtonColors(
                                        contentColor = Color(0xFFEF4444) // 红色
                                        )
                                    ) {
                                        Text("清空")
                                }
                                
                                // 打单按钮
                                Button(
                                    onClick = {
                                        // 使用购物车内容创建打单
                                        betNumbers = betCart.flatMap { item ->
                                            when (item.betType) {
                                                BetType.SPECIAL_NUMBER -> 
                                                    item.numbers.map { BetNumber(listOf(it)) }
                                                BetType.TWO_IN_TWO, BetType.TWO_IN_TWO_COMPLEX ->
                                                    if (item.isComplex) {
                                                            item.numbers.combinations(2).map { 
                                                                BetNumber(it, isComplex = true) 
                                                            }
                                                    } else {
                                                            item.numbers.sequentialPairs().map { 
                                                                BetNumber(it) 
                                                            }
                                                    }
                                                BetType.THREE_IN_THREE, BetType.THREE_IN_THREE_COMPLEX,
                                                BetType.TWO_IN_THREE, BetType.TWO_IN_THREE_COMPLEX ->
                                                    if (item.isComplex) {
                                                            item.numbers.combinations(3).map { 
                                                                BetNumber(it, isComplex = true) 
                                                            }
                                                    } else {
                                                            item.numbers.sequentialTriples().map { 
                                                                BetNumber(it) 
                                                            }
                                                    }
                                            }
                                        }
                                        selectedBetType = null
                                        showBetDialog = true
                                        }
                                    ) {
                                        Text("打单")
                                    }
                                }
                            }
                            
                            // 购物车内容摘要
                                        Text(
                                text = "已添加号码: ${betCart.sumOf { it.numbers.size }}个",
                                style = MaterialTheme.typography.bodyMedium,
                                color = MaterialTheme.colorScheme.onSurfaceVariant,
                                modifier = Modifier.padding(vertical = 4.dp)
                            )
                            
                            // 添加号码预览
                            if (betCart.isNotEmpty()) {
                            Divider(
                                    color = MaterialTheme.colorScheme.outline.copy(alpha = 0.3f),
                                modifier = Modifier.padding(vertical = 4.dp)
                            )
                            
                                LazyRow(
                                    horizontalArrangement = Arrangement.spacedBy(8.dp),
                                    contentPadding = PaddingValues(vertical = 8.dp),
                                            modifier = Modifier
                                                .fillMaxWidth()
                                        .heightIn(max = 88.dp)
                                ) {
                                    items(betCart) { item ->
                                        Card(
                                            shape = RoundedCornerShape(8.dp),
                                            border = BorderStroke(1.dp, MaterialTheme.colorScheme.outline.copy(alpha = 0.3f)),
                                            colors = CardDefaults.cardColors(
                                                containerColor = MaterialTheme.colorScheme.surfaceVariant.copy(alpha = 0.3f)
                                            ),
                                            modifier = Modifier.width(100.dp)
                                        ) {
                                            Column(
                                                modifier = Modifier.padding(8.dp),
                                                horizontalAlignment = Alignment.CenterHorizontally
                                            ) {
                                                // 显示投注类型
                                                Text(
                                                    text = when (item.betType) {
                                                        BetType.SPECIAL_NUMBER -> "特码"
                                                        BetType.TWO_IN_TWO -> "二中二"
                                                        BetType.TWO_IN_TWO_COMPLEX -> "二中二(复)"
                                                        BetType.THREE_IN_THREE -> "三中三"
                                                        BetType.THREE_IN_THREE_COMPLEX -> "三中三(复)"
                                                        BetType.TWO_IN_THREE -> "三中二"
                                                        BetType.TWO_IN_THREE_COMPLEX -> "三中二(复)"
                                                    },
                                                    fontSize = 12.sp,
                                                    fontWeight = FontWeight.Bold,
                                                    color = MaterialTheme.colorScheme.primary
                                                )
                                                
                                                // 显示号码
                                                Box(
                                                    modifier = Modifier
                                                        .padding(vertical = 4.dp)
                                                        .fillMaxWidth(),
                                                    contentAlignment = Alignment.Center
                                                ) {
                                                    // 只显示前几个号码，如果太多则显示省略号
                                                    val displayNumbers = if (item.numbers.size > 3) 
                                                        item.numbers.take(3) + "..." 
                                                    else 
                                                        item.numbers
                                                    
                                                Text(
                                                        text = displayNumbers.joinToString(" "),
                                                    fontSize = 12.sp,
                                                        color = MaterialTheme.colorScheme.onSurfaceVariant,
                                                        maxLines = 1,
                                                        overflow = TextOverflow.Ellipsis
                                                    )
                                                }
                                                
                                                // 显示数量
                                                Text(
                                                    text = "${item.numbers.size}个",
                                                    fontSize = 11.sp,
                                                    color = Color(0xFF8B5CF6)
                                                )
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    // 添加对话框
    if (showYearSettingDialog) {
        YearSettingDialog(
            onDismiss = { showYearSettingDialog = false },
            onConfirm = onYearSettingConfirm
        )
    }

    // 打单对话框
    if (showBetDialog) {
        AlertDialog(
            onDismissRequest = { showBetDialog = false },
            title = { 
                Column {
                    Text(
                        text = when (selectedBetType) {
                            BetType.SPECIAL_NUMBER -> "特码"
                            BetType.TWO_IN_TWO -> "二中二"
                            BetType.TWO_IN_TWO_COMPLEX -> "二中二(复式)"
                            BetType.THREE_IN_THREE -> "三中三"
                            BetType.THREE_IN_THREE_COMPLEX -> "三中三(复式)"
                            BetType.TWO_IN_THREE -> "三中二"
                            BetType.TWO_IN_THREE_COMPLEX -> "三中二(复式)"
                            null -> ""
                        },
                        style = MaterialTheme.typography.titleLarge,
                        color = MaterialTheme.colorScheme.primary
                    )
                    Divider(
                        modifier = Modifier.padding(top = 8.dp),
                        color = MaterialTheme.colorScheme.outlineVariant
                    )
                }
            },
            text = {
                Column(
                    modifier = Modifier.fillMaxWidth(),
                    verticalArrangement = Arrangement.spacedBy(12.dp)
                ) {
                    // 金额统计信息卡片 - 调整为更紧凑的设计
                    Card(
                        modifier = Modifier.fillMaxWidth(),
                        colors = CardDefaults.cardColors(
                            containerColor = MaterialTheme.colorScheme.primaryContainer.copy(alpha = 0.7f)
                        ),
                        elevation = CardDefaults.cardElevation(defaultElevation = 1.dp)
                    ) {
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(12.dp),
                            horizontalArrangement = Arrangement.SpaceEvenly
                        ) {
                            Column(
                                horizontalAlignment = Alignment.CenterHorizontally
                            ) {
                                Text(
                                    text = "总注数",
                                    style = MaterialTheme.typography.bodyMedium,
                                    color = MaterialTheme.colorScheme.onPrimaryContainer.copy(alpha = 0.7f)
                                )
                                Text(
                                    text = "${betNumbers.count { it.amount > 0 }}",
                                    style = MaterialTheme.typography.titleLarge,
                                    color = MaterialTheme.colorScheme.onPrimaryContainer,
                                    fontWeight = FontWeight.Bold
                                )
                            }
                            
                            Divider(
                                modifier = Modifier
                                    .height(36.dp)
                                    .width(1.dp),
                                color = MaterialTheme.colorScheme.onPrimaryContainer.copy(alpha = 0.2f)
                            )
                            
                            Column(
                                horizontalAlignment = Alignment.CenterHorizontally
                            ) {
                                Text(
                                    text = "总金额",
                                    style = MaterialTheme.typography.bodyMedium,
                                    color = MaterialTheme.colorScheme.onPrimaryContainer.copy(alpha = 0.7f)
                                )
                                Text(
                                    text = "${betNumbers.sumOf { it.amount }}",
                                    style = MaterialTheme.typography.titleLarge,
                                    color = MaterialTheme.colorScheme.onPrimaryContainer,
                                    fontWeight = FontWeight.Bold
                                )
                            }
                        }
                    }

                    // 统一金额输入框 - 将文字放在边框上
                    Box(modifier = Modifier.fillMaxWidth()) {
                            OutlinedTextField(
                                value = unifiedAmount,
                                onValueChange = { newValue ->
                                    if (newValue.isEmpty() || newValue.all { it.isDigit() }) {
                                        unifiedAmount = newValue
                                        val newAmount = newValue.toIntOrNull() ?: 0
                                        betNumbers = betNumbers.map { it.copy(amount = newAmount) }
                                    }
                                },
                                keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
                                modifier = Modifier
                                    .fillMaxWidth()
                                .height(58.dp),
                                singleLine = true,
                            label = { Text("统一金额") },
                                colors = OutlinedTextFieldDefaults.colors(
                                    focusedBorderColor = MaterialTheme.colorScheme.primary,
                                unfocusedBorderColor = MaterialTheme.colorScheme.outline,
                                focusedContainerColor = Color.White,
                                unfocusedContainerColor = Color.White
                            ),
                            textStyle = LocalTextStyle.current.copy(
                                textAlign = TextAlign.Center,
                                fontSize = 16.sp
                            )
                        )
                    }

                    // 单独设置区域 - 修改为符合要求的布局
                    Surface(
                        modifier = Modifier.fillMaxWidth(),
                        shape = RoundedCornerShape(8.dp),
                        color = MaterialTheme.colorScheme.surfaceVariant.copy(alpha = 0.3f)
                    ) {
                        Column(
                            modifier = Modifier.padding(vertical = 8.dp)
                        ) {
                            // 标题
                            Text(
                                text = "单独设置",
                                style = MaterialTheme.typography.titleMedium,
                                modifier = Modifier.padding(horizontal = 16.dp, vertical = 4.dp),
                                color = MaterialTheme.colorScheme.primary
                            )
                            
                            Divider(color = MaterialTheme.colorScheme.outline.copy(alpha = 0.3f))
                            
                            // 将投注分为三类
                            val specialBets = betNumbers.filter { it.numbers.size == 1 }
                            val twoInTwoBets = betNumbers.filter { it.numbers.size == 2 }
                            val threeInThreeBets = betNumbers.filter { it.numbers.size == 3 }
                            
                            LazyColumn(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .heightIn(min = 120.dp, max = 360.dp),
                                contentPadding = PaddingValues(horizontal = 8.dp, vertical = 4.dp),
                                verticalArrangement = Arrangement.spacedBy(8.dp)
                            ) {
                                // 1. 特码布局 - 一行显示两组（号码+输入框）
                                val specialRowCount = (specialBets.size + 1) / 2
                                items(specialRowCount) { rowIndex ->
                                    Row(
                                        modifier = Modifier.fillMaxWidth(),
                                        horizontalArrangement = Arrangement.spacedBy(8.dp)
                                    ) {
                                        for (colIndex in 0 until 2) {
                                            val index = rowIndex * 2 + colIndex
                                            if (index < specialBets.size) {
                                                // 特码格子 - 水平布局：号码+输入框
                                    Surface(
                                                    modifier = Modifier.weight(1f),
                                                    shape = RoundedCornerShape(4.dp),
                                                    color = MaterialTheme.colorScheme.surface,
                                                    border = BorderStroke(1.dp, Color.LightGray.copy(alpha = 0.5f))
                                    ) {
                                        Row(
                                                        modifier = Modifier.padding(6.dp),
                                                        verticalAlignment = Alignment.CenterVertically
                                                    ) {
                                                        // 号码部分
                                                        val bet = specialBets[index]
                                                        val num = bet.numbers[0].toInt()
                                                        val color = getNumberColor(num)
                                                        val zodiac = ZodiacUtils.getZodiacForNumber(num) ?: ""
                                                        
                                                        // 使用方形号码显示
                                                        Surface(
                                                            shape = RoundedCornerShape(4.dp),
                                                            color = color.copy(alpha = 0.1f),
                                                            border = BorderStroke(1.dp, color),
                                                            modifier = Modifier.size(36.dp)
                                                        ) {
                                                            Column(
                                                                modifier = Modifier.fillMaxSize(),
                                                                horizontalAlignment = Alignment.CenterHorizontally,
                                                                verticalArrangement = Arrangement.Center
                                                            ) {
                                                                Text(
                                                                    text = num.toString(),
                                                                    fontSize = 14.sp,
                                                                    fontWeight = FontWeight.Bold,
                                                                    color = color
                                                                )
                                                                Text(
                                                                    text = zodiac,
                                                                    fontSize = 10.sp,
                                                                    color = color
                                                                )
                                                            }
                                                        }
                                                        
                                                        // 右侧输入框
                                                        BasicTextField(
                                                            value = if (bet.amount > 0) bet.amount.toString() else "",
                                                            onValueChange = { newValue ->
                                                                if (newValue.isEmpty() || newValue.all { it.isDigit() }) {
                                                                    val newAmount = newValue.toIntOrNull() ?: 0
                                                                    betNumbers = betNumbers.map { currentBet -> 
                                                                        if (currentBet == bet) currentBet.copy(amount = newAmount) else currentBet 
                                                                    }
                                                                }
                                                            },
                                                            keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
                                                            textStyle = androidx.compose.ui.text.TextStyle(
                                                                fontSize = 16.sp,
                                                                color = Color.Black,
                                                                textAlign = TextAlign.Center
                                                            ),
                                                            singleLine = true,
                                            modifier = Modifier
                                                                .weight(1f)
                                                                .height(36.dp)
                                                                .padding(start = 8.dp)
                                                                .background(Color.White, RoundedCornerShape(4.dp))
                                                                .border(1.dp, Color.Gray.copy(alpha = 0.4f), RoundedCornerShape(4.dp))
                                                                .padding(horizontal = 4.dp, vertical = 8.dp)
                                                        )
                                                    }
                                                }
                                            } else {
                                                Spacer(modifier = Modifier.weight(1f))
                                            }
                                        }
                                    }
                                }
                                
                                // 2. 二中二布局 - 一行显示两个号码+输入框
                                items(twoInTwoBets) { bet ->
                                    Surface(
                                        modifier = Modifier.fillMaxWidth(),
                                        shape = RoundedCornerShape(4.dp),
                                        color = MaterialTheme.colorScheme.surface,
                                        border = BorderStroke(1.dp, Color.LightGray.copy(alpha = 0.5f))
                                    ) {
                                        Column(
                                            modifier = Modifier.padding(8.dp)
                                        ) {
                                            // 显示二中二信息
                                            Row(
                                                modifier = Modifier.fillMaxWidth(),
                                                horizontalArrangement = Arrangement.SpaceBetween,
                                                verticalAlignment = Alignment.CenterVertically
                                            ) {
                                                // 左侧显示两个号码
                                                Row(
                                                    horizontalArrangement = Arrangement.spacedBy(8.dp),
                                            verticalAlignment = Alignment.CenterVertically,
                                                    modifier = Modifier.weight(1f)
                                                ) {
                                                    bet.numbers.forEach { numberStr ->
                                                        val num = numberStr.toInt()
                                                        val color = getNumberColor(num)
                                                        val zodiac = ZodiacUtils.getZodiacForNumber(num) ?: ""
                                                        
                                                        // 号码显示
                                                        Surface(
                                                            shape = CircleShape,
                                                            color = color.copy(alpha = 0.1f),
                                                            border = BorderStroke(1.dp, color),
                                                            modifier = Modifier.size(36.dp)
                                                        ) {
                                                            Column(
                                                                modifier = Modifier.fillMaxSize(),
                                                                horizontalAlignment = Alignment.CenterHorizontally,
                                                                verticalArrangement = Arrangement.Center
                                        ) {
                                            Text(
                                                                    text = num.toString(),
                                                                    fontSize = 14.sp,
                                                                    fontWeight = FontWeight.Bold,
                                                                    color = color
                                                                )
                                                                Text(
                                                                    text = zodiac,
                                                                    fontSize = 10.sp,
                                                                    color = color
                                                                )
                                                            }
                                                        }
                                                    }
                                                    
                                                    // 添加说明文字
                                                    Text(
                                                        text = "二中二",
                                                        fontSize = 12.sp,
                                                        color = Color.Gray,
                                                        modifier = Modifier.padding(start = 4.dp)
                                                    )
                                                }
                                                
                                                // 右侧输入框
                                                BasicTextField(
                                                value = if (bet.amount > 0) bet.amount.toString() else "",
                                                onValueChange = { newValue ->
                                                    if (newValue.isEmpty() || newValue.all { it.isDigit() }) {
                                                        val newAmount = newValue.toIntOrNull() ?: 0
                                                            betNumbers = betNumbers.map { currentBet -> 
                                                                if (currentBet == bet) currentBet.copy(amount = newAmount) else currentBet 
                                                        }
                                                    }
                                                },
                                                keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
                                                    textStyle = androidx.compose.ui.text.TextStyle(
                                                        fontSize = 16.sp,
                                                        color = Color.Black,
                                                        textAlign = TextAlign.Center
                                                    ),
                                                singleLine = true,
                                                    modifier = Modifier
                                                        .width(110.dp)
                                                        .height(36.dp)
                                                        .background(Color.White, RoundedCornerShape(4.dp))
                                                        .border(1.dp, Color.Gray.copy(alpha = 0.4f), RoundedCornerShape(4.dp))
                                                        .padding(horizontal = 4.dp, vertical = 8.dp)
                                                )
                                            }
                                        }
                                    }
                                }
                                
                                // 3. 三中三布局 - 一行显示三个号码+输入框
                                items(threeInThreeBets) { bet ->
                                    Surface(
                                        modifier = Modifier.fillMaxWidth(),
                                        shape = RoundedCornerShape(4.dp),
                                        color = MaterialTheme.colorScheme.surface,
                                        border = BorderStroke(1.dp, Color.LightGray.copy(alpha = 0.5f))
                                    ) {
                                        Column(
                                            modifier = Modifier.padding(8.dp)
                                        ) {
                                            // 显示三中三信息
                                            Row(
                                                modifier = Modifier.fillMaxWidth(),
                                                horizontalArrangement = Arrangement.SpaceBetween,
                                                verticalAlignment = Alignment.CenterVertically
                                            ) {
                                                // 左侧显示三个号码
                                                Row(
                                                    horizontalArrangement = Arrangement.spacedBy(4.dp),
                                                    verticalAlignment = Alignment.CenterVertically,
                                                    modifier = Modifier.weight(1f)
                                                ) {
                                                    bet.numbers.forEach { numberStr ->
                                                        val num = numberStr.toInt()
                                                        val color = getNumberColor(num)
                                                        val zodiac = ZodiacUtils.getZodiacForNumber(num) ?: ""
                                                        
                                                        // 号码显示
                                                        Surface(
                                                            shape = CircleShape,
                                                            color = color.copy(alpha = 0.1f),
                                                            border = BorderStroke(1.dp, color),
                                                            modifier = Modifier.size(32.dp)
                                                        ) {
                                                            Column(
                                                                modifier = Modifier.fillMaxSize(),
                                                                horizontalAlignment = Alignment.CenterHorizontally,
                                                                verticalArrangement = Arrangement.Center
                                                            ) {
                                                                Text(
                                                                    text = num.toString(),
                                                                    fontSize = 12.sp,
                                                                    fontWeight = FontWeight.Bold,
                                                                    color = color
                                                                )
                                                                Text(
                                                                    text = zodiac,
                                                                    fontSize = 8.sp,
                                                                    color = color
                                                                )
                                                            }
                                                        }
                                                    }
                                                    
                                                    // 添加说明文字
                                                    Text(
                                                        text = "三中三",
                                                        fontSize = 11.sp,
                                                        color = Color.Gray,
                                                        modifier = Modifier.padding(start = 4.dp)
                                                    )
                                                }
                                                
                                                // 右侧输入框
                                                BasicTextField(
                                                    value = if (bet.amount > 0) bet.amount.toString() else "",
                                                    onValueChange = { newValue ->
                                                        if (newValue.isEmpty() || newValue.all { it.isDigit() }) {
                                                            val newAmount = newValue.toIntOrNull() ?: 0
                                                            betNumbers = betNumbers.map { currentBet -> 
                                                                if (currentBet == bet) currentBet.copy(amount = newAmount) else currentBet 
                                                            }
                                                        }
                                                    },
                                                    keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
                                                    textStyle = androidx.compose.ui.text.TextStyle(
                                                        fontSize = 16.sp,
                                                        color = Color.Black,
                                                        textAlign = TextAlign.Center
                                                    ),
                                                    singleLine = true,
                                                    modifier = Modifier
                                                        .width(90.dp)
                                                        .height(36.dp)
                                                        .background(Color.White, RoundedCornerShape(4.dp))
                                                        .border(1.dp, Color.Gray.copy(alpha = 0.4f), RoundedCornerShape(4.dp))
                                                        .padding(horizontal = 4.dp, vertical = 8.dp)
                                                )
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            },
            confirmButton = {
                Button(
                    onClick = {
                        // 修改打单文本生成逻辑
                        val betText = StringBuilder().apply {
                            // 使用已设置金额的betNumbers生成打单文本
                            // 按投注类型分组
                            val betsByType = mutableMapOf<BetType, MutableList<BetNumber>>()
                            
                            // 将betNumbers按类型分组
                            betNumbers.forEach { bet ->
                                val type = when {
                                    bet.numbers.size == 1 -> BetType.SPECIAL_NUMBER
                                    bet.numbers.size == 2 && !bet.isComplex -> BetType.TWO_IN_TWO
                                    bet.numbers.size == 2 && bet.isComplex -> BetType.TWO_IN_TWO_COMPLEX
                                    bet.numbers.size == 3 && !bet.isComplex -> {
                                        // 这里需要根据实际情况判断是三中三还是三中二
                                        // 简化处理，假设都是三中三
                                        BetType.THREE_IN_THREE
                                    }
                                    bet.numbers.size == 3 && bet.isComplex -> {
                                        // 同样需要判断
                                        BetType.THREE_IN_THREE_COMPLEX
                                    }
                                    else -> BetType.SPECIAL_NUMBER // 默认
                                }
                                
                                betsByType.getOrPut(type) { mutableListOf() }.add(bet)
                            }
                            
                            // 处理特码 - 修改为同一金额的号码在同一行
                            betsByType[BetType.SPECIAL_NUMBER]?.let { specialBets ->
                                if (specialBets.isNotEmpty()) {
                                    append("特码\n")
                                    
                                    // 按金额分组处理
                                    val betsByAmount = specialBets.groupBy { it.amount }
                                    
                                    betsByAmount.forEach { (amount, bets) ->
                                        // 只处理有金额的投注
                                        if (amount > 0) {
                                            // 同一金额的号码在同一行，用空格分隔
                                            val numbers = bets.map { it.numbers.first() }
                                                .joinToString(" ")
                                            
                                            append(numbers)
                                            append("各")
                                            append(amount)
                                            append("元\n")
                                        }
                                    }
                                    append("\n")
                                }
                            }
                            
                            // 处理二中二 - 修改为一行一列格式显示
                            val twoInTwoBets = betsByType[BetType.TWO_IN_TWO]
                            val twoInTwoComplexBets = betsByType[BetType.TWO_IN_TWO_COMPLEX]
                            
                            if (!twoInTwoBets.isNullOrEmpty() || !twoInTwoComplexBets.isNullOrEmpty()) {
                                append("二中二\n")
                                
                                // 处理直选，按金额分组并改为一行显示
                                twoInTwoBets?.groupBy { it.amount }?.forEach { (amount, bets) ->
                                    if (amount > 0) {
                                        // 将所有组合在一行中显示，用逗号分隔
                                        val combinations = bets.joinToString(" | ") { it.numbers.joinToString(",") }
                                        append(combinations)
                                        append("各")
                                        append(amount)
                                        append("元\n")
                                    }
                                }
                                
                                // 处理复式，按金额分组并改为一行显示
                                twoInTwoComplexBets?.groupBy { it.amount }?.forEach { (amount, bets) ->
                                    if (amount > 0) {
                                        // 将所有组合在一行中显示，用逗号分隔
                                        val combinations = bets.joinToString(" | ") { it.numbers.joinToString(",") }
                                        append(combinations)
                                        append("各")
                                        append(amount)
                                        append("元\n")
                                    }
                                }
                                append("\n")
                            }
                            
                            // 处理三中三 - 修改为一行一列格式显示
                            val threeInThreeBets = betsByType[BetType.THREE_IN_THREE]
                            val threeInThreeComplexBets = betsByType[BetType.THREE_IN_THREE_COMPLEX]
                            
                            if (!threeInThreeBets.isNullOrEmpty() || !threeInThreeComplexBets.isNullOrEmpty()) {
                                append("三中三\n")
                                
                                // 处理直选，按金额分组并改为一行显示
                                threeInThreeBets?.groupBy { it.amount }?.forEach { (amount, bets) ->
                                    if (amount > 0) {
                                        // 将所有组合在一行中显示，用竖线分隔
                                        val combinations = bets.joinToString(" | ") { it.numbers.joinToString(",") }
                                        append(combinations)
                                        append("各")
                                        append(amount)
                                        append("元\n")
                                    }
                                }
                                
                                // 处理复式，按金额分组并改为一行显示
                                threeInThreeComplexBets?.groupBy { it.amount }?.forEach { (amount, bets) ->
                                    if (amount > 0) {
                                        // 将所有组合在一行中显示，用竖线分隔
                                        val combinations = bets.joinToString(" | ") { it.numbers.joinToString(",") }
                                        append(combinations)
                                        append("各")
                                        append(amount)
                                        append("元\n")
                                    }
                                }
                                append("\n")
                            }
                            
                            // 处理三中二 - 修改为一行一列格式显示
                            val twoInThreeBets = betsByType[BetType.TWO_IN_THREE]
                            val twoInThreeComplexBets = betsByType[BetType.TWO_IN_THREE_COMPLEX]
                            
                            if (!twoInThreeBets.isNullOrEmpty() || !twoInThreeComplexBets.isNullOrEmpty()) {
                                append("三中二\n")
                                
                                // 处理直选，按金额分组并改为一行显示
                                twoInThreeBets?.groupBy { it.amount }?.forEach { (amount, bets) ->
                                    if (amount > 0) {
                                        // 将所有组合在一行中显示，用竖线分隔
                                        val combinations = bets.joinToString(" | ") { it.numbers.joinToString(",") }
                                        append(combinations)
                                        append("各")
                                        append(amount)
                                        append("元\n")
                                    }
                                }
                                
                                // 处理复式，按金额分组并改为一行显示
                                twoInThreeComplexBets?.groupBy { it.amount }?.forEach { (amount, bets) ->
                                    if (amount > 0) {
                                        // 将所有组合在一行中显示，用竖线分隔
                                        val combinations = bets.joinToString(" | ") { it.numbers.joinToString(",") }
                                        append(combinations)
                                        append("各")
                                        append(amount)
                                        append("元\n")
                                    }
                                }
                            }
                        }.toString()
                        
                        // 分享打单文本
                        val sendIntent = Intent().apply {
                            action = Intent.ACTION_SEND
                            putExtra(Intent.EXTRA_TEXT, betText)
                            type = "text/plain"
                        }
                        context.startActivity(Intent.createChooser(sendIntent, "分享打单"))
                        showBetDialog = false
                        betCart = emptyList() // 清空购物车
                    },
                    colors = ButtonDefaults.buttonColors(
                        containerColor = MaterialTheme.colorScheme.primary
                    )
                ) {
                    Text("确认打单")
                }
            },
            dismissButton = {
                TextButton(onClick = { showBetDialog = false }) {
                    Text("取消")
                }
            }
        )
    }

    // 修改投注方式选择对话框的处理逻辑
    if (showBetTypeDialog) {
        AlertDialog(
            onDismissRequest = { showBetTypeDialog = false },
            title = { Text("选择投注方式") },
            text = {
                Column(
                    modifier = Modifier.fillMaxWidth(),
                    verticalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    // 特码选项
                    if (selectedNumbers.isNotEmpty()) {
                        FilledTonalButton(
                            onClick = {
                                if (isAddingToBetCart) {
                                    // 添加到购物车
                                    val newItem = BetCartItem(
                                        betType = BetType.SPECIAL_NUMBER,
                                        numbers = selectedNumbers.toList()
                                    )
                                    betCart = betCart + newItem
                                    selectedNumbers = emptySet()
                                } else {
                                    // 直接打单
                                    selectedBetType = BetType.SPECIAL_NUMBER
                                    betNumbers = selectedNumbers.map { BetNumber(listOf(it)) }
                                    showBetDialog = true
                                }
                                showBetTypeDialog = false
                            },
                            modifier = Modifier.fillMaxWidth(),
                            colors = ButtonDefaults.filledTonalButtonColors(
                                containerColor = MaterialTheme.colorScheme.secondaryContainer.copy(alpha = 0.3f)
                            )
                        ) {
                            Text("特码")
                        }
                    }

                    // 二中二分类
                    if (selectedNumbers.size >= 2) {
                        Text(
                            text = "二中二",
                            style = MaterialTheme.typography.titleSmall,
                            color = MaterialTheme.colorScheme.primary,
                            modifier = Modifier.padding(top = 8.dp)
                        )
                        
                        // 直接
                        FilledTonalButton(
                            onClick = {
                                if (isAddingToBetCart) {
                                    val newItem = BetCartItem(
                                        betType = BetType.TWO_IN_TWO,
                                        numbers = selectedNumbers.toList()
                                    )
                                    betCart = betCart + newItem
                                    selectedNumbers = emptySet()
                                } else {
                                    selectedBetType = BetType.TWO_IN_TWO
                                    val pairs = selectedNumbers.toList().sequentialPairs()
                                    betNumbers = pairs.map { combo -> BetNumber(numbers = combo) }
                                    showBetDialog = true
                                }
                                showBetTypeDialog = false
                            },
                            modifier = Modifier.fillMaxWidth(),
                            colors = ButtonDefaults.filledTonalButtonColors(
                                containerColor = MaterialTheme.colorScheme.secondaryContainer.copy(alpha = 0.3f)
                            )
                        ) {
                            Text("二中二")
                        }
                        
                        // 复式
                        if (selectedNumbers.size > 2) {
                            FilledTonalButton(
                                onClick = {
                                    if (isAddingToBetCart) {
                                        val newItem = BetCartItem(
                                            betType = BetType.TWO_IN_TWO_COMPLEX,
                                            numbers = selectedNumbers.toList(),
                                            isComplex = true
                                        )
                                        betCart = betCart + newItem
                                        selectedNumbers = emptySet()
                                    } else {
                                        selectedBetType = BetType.TWO_IN_TWO_COMPLEX
                                        val combinations = selectedNumbers.toList().combinations(2)
                                        betNumbers = combinations.map { combo -> 
                                            BetNumber(numbers = combo, isComplex = true) 
                                        }
                                        showBetDialog = true
                                    }
                                    showBetTypeDialog = false
                                },
                                modifier = Modifier.fillMaxWidth(),
                                colors = ButtonDefaults.filledTonalButtonColors(
                                    containerColor = MaterialTheme.colorScheme.secondaryContainer.copy(alpha = 0.3f)
                                )
                            ) {
                                Text("二中二(复式)")
                            }
                        }
                    }

                    // 三中三分类
                    if (selectedNumbers.size >= 3) {
                        Text(
                            text = "三中三",
                            style = MaterialTheme.typography.titleSmall,
                            color = MaterialTheme.colorScheme.primary,
                            modifier = Modifier.padding(top = 8.dp)
                        )
                        
                        // 直接
                        FilledTonalButton(
                            onClick = {
                                if (isAddingToBetCart) {
                                    val newItem = BetCartItem(
                                        betType = BetType.THREE_IN_THREE,
                                        numbers = selectedNumbers.toList()
                                    )
                                    betCart = betCart + newItem
                                    selectedNumbers = emptySet()
                                } else {
                                    selectedBetType = BetType.THREE_IN_THREE
                                    val triples = selectedNumbers.toList().sequentialTriples()
                                    betNumbers = triples.map { combo -> BetNumber(numbers = combo) }
                                    showBetDialog = true
                                }
                                showBetTypeDialog = false
                            },
                            modifier = Modifier.fillMaxWidth(),
                            colors = ButtonDefaults.filledTonalButtonColors(
                                containerColor = MaterialTheme.colorScheme.secondaryContainer.copy(alpha = 0.3f)
                            )
                        ) {
                            Text("三中三")
                        }
                        
                        // 复式
                        if (selectedNumbers.size > 3) {
                            FilledTonalButton(
                                onClick = {
                                    if (isAddingToBetCart) {
                                        val newItem = BetCartItem(
                                            betType = BetType.THREE_IN_THREE_COMPLEX,
                                            numbers = selectedNumbers.toList(),
                                            isComplex = true
                                        )
                                        betCart = betCart + newItem
                                        selectedNumbers = emptySet()
                                    } else {
                                        selectedBetType = BetType.THREE_IN_THREE_COMPLEX
                                        val combinations = selectedNumbers.toList().combinations(3)
                                        betNumbers = combinations.map { combo -> 
                                            BetNumber(numbers = combo, isComplex = true) 
                                        }
                                        showBetDialog = true
                                    }
                                    showBetTypeDialog = false
                                },
                                modifier = Modifier.fillMaxWidth(),
                                colors = ButtonDefaults.filledTonalButtonColors(
                                    containerColor = MaterialTheme.colorScheme.secondaryContainer.copy(alpha = 0.3f)
                                )
                            ) {
                                Text("三中三(复式)")
                            }
                        }
                    }

                    // 三中二分类
                    if (selectedNumbers.size >= 3) {
                        Text(
                            text = "三中二",
                            style = MaterialTheme.typography.titleSmall,
                            color = MaterialTheme.colorScheme.primary,
                            modifier = Modifier.padding(top = 8.dp)
                        )
                        
                        // 直接
                        FilledTonalButton(
                            onClick = {
                                if (isAddingToBetCart) {
                                    val newItem = BetCartItem(
                                        betType = BetType.TWO_IN_THREE,
                                        numbers = selectedNumbers.toList()
                                    )
                                    betCart = betCart + newItem
                                    selectedNumbers = emptySet()
                                } else {
                                    selectedBetType = BetType.TWO_IN_THREE
                                    val triples = selectedNumbers.toList().sequentialTriples()
                                    betNumbers = triples.map { combo -> BetNumber(numbers = combo) }
                                    showBetDialog = true
                                }
                                showBetTypeDialog = false
                            },
                            modifier = Modifier.fillMaxWidth(),
                            colors = ButtonDefaults.filledTonalButtonColors(
                                containerColor = MaterialTheme.colorScheme.secondaryContainer.copy(alpha = 0.3f)
                            )
                        ) {
                            Text("三中二")
                        }
                        
                        // 复式
                        if (selectedNumbers.size > 3) {
                            FilledTonalButton(
                                onClick = {
                                    if (isAddingToBetCart) {
                                        val newItem = BetCartItem(
                                            betType = BetType.TWO_IN_THREE_COMPLEX,
                                            numbers = selectedNumbers.toList(),
                                            isComplex = true
                                        )
                                        betCart = betCart + newItem
                                        selectedNumbers = emptySet()
                                    } else {
                                        selectedBetType = BetType.TWO_IN_THREE_COMPLEX
                                        val combinations = selectedNumbers.toList().combinations(3)
                                        betNumbers = combinations.map { combo -> 
                                            BetNumber(numbers = combo, isComplex = true) 
                                        }
                                        showBetDialog = true
                                    }
                                    showBetTypeDialog = false
                                },
                                modifier = Modifier.fillMaxWidth(),
                                colors = ButtonDefaults.filledTonalButtonColors(
                                    containerColor = MaterialTheme.colorScheme.secondaryContainer.copy(alpha = 0.3f)
                                )
                            ) {
                                Text("三中二(复式)")
                            }
                        }
                    }
                }
            },
            confirmButton = { },  // 移除确认按钮,因为我们在每个选项按钮中都处理了逻辑
            dismissButton = {
                TextButton(onClick = { showBetTypeDialog = false }) {
                    Text("取消")
                }
            }
        )
    }
}

@Composable
fun NumberChip(
    number: String,
    color: Color = MaterialTheme.colorScheme.primary,
    zodiac: String? = null,
    isSelected: Boolean = false,
    onClick: () -> Unit = {}
) {
    // 获取是否为平板模式
    val isTabletMode = isTablet()
    
    Surface(
        shape = RoundedCornerShape(8.dp),
        color = if (isSelected) color.copy(alpha = 0.2f) else color.copy(alpha = 0.08f),
        border = BorderStroke(
            width = if (isSelected) 2.dp else 1.dp,
            color = if (isSelected) color else color.copy(alpha = 0.3f)
        ),
        modifier = Modifier
            .aspectRatio(1f)
            .clickable(onClick = onClick)
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(0.dp),
            verticalArrangement = Arrangement.Center,
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            // 处理号码显示，去掉前导零
            val displayNumber = number.toIntOrNull()?.toString() ?: number
            Text(
                text = displayNumber,
                color = color,
                // 在平板模式下使用更大的字体
                fontSize = if (isTabletMode) 24.sp else 20.sp,
                fontWeight = FontWeight.Medium,
                textAlign = TextAlign.Center,
                modifier = Modifier.padding(bottom = 1.dp)
            )
            
            zodiac?.let {
                Text(
                    text = it,
                    color = color.copy(alpha = 0.7f),
                    // 在平板模式下使用更大的字体
                    fontSize = if (isTabletMode) 14.sp else 11.sp,
                    textAlign = TextAlign.Center,
                    modifier = Modifier.offset(y = (-5).dp)
                )
            }
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun FilterButton(
    text: String,
    isSelected: Boolean,
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
    allNumbers: List<String> = emptyList()
) {
    val colors = when (text) {
        "红波" -> Color.Red
        "绿波" -> Color(0xFF228B22)
        "蓝波" -> Color.Blue
        else -> MaterialTheme.colorScheme.primary
    }

    // 修改为更美观的筛选按钮
    Surface(
        shape = RoundedCornerShape(8.dp),
        border = BorderStroke(
            width = if (isSelected) 2.dp else 1.dp,
            color = if (isSelected) colors else colors.copy(alpha = 0.3f)
        ),
        color = if (isSelected) colors.copy(alpha = 0.1f) else Color.White,
        modifier = modifier
            .height(40.dp)
            .clickable(onClick = onClick)
    ) {
        Box(
            modifier = Modifier.fillMaxSize(),
                contentAlignment = Alignment.Center
            ) {
                Text(
                    text = text,
                fontSize = 16.sp,
                    fontWeight = if(isSelected) FontWeight.Bold else FontWeight.Normal,
                    textAlign = TextAlign.Center,
                    color = if (isSelected) colors else MaterialTheme.colorScheme.onSurface
                )
            }
    }
}

@Composable
fun FilterHeader(
    modifier: Modifier = Modifier,
    onFilterClick: () -> Unit,
    onYearSettingClick: () -> Unit,
    onHistoryClick: () -> Unit,
    onPredictionClick: () -> Unit = {},
    onSmartPredictionClick: () -> Unit = {}
) {
    Row(
        modifier = modifier
            .fillMaxWidth()
            .padding(horizontal = 16.dp, vertical = 8.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Text(
            text = "筛选条件",
            style = MaterialTheme.typography.titleMedium,
            modifier = Modifier.weight(1f)
        )
        
        // 添加智能预测按钮
        TextButton(
            onClick = onSmartPredictionClick,
            modifier = Modifier.padding(end = 8.dp),
            colors = ButtonDefaults.textButtonColors(
                contentColor = MaterialTheme.colorScheme.primary
            )
        ) {
            Text("智能预测")
        }

        // 添加预测按钮
        TextButton(
            onClick = onPredictionClick,
            modifier = Modifier.padding(end = 8.dp)
        ) {
            Text("下一期预测")
        }
        
        // 添加开奖历史按钮
        TextButton(
            onClick = onHistoryClick,
            modifier = Modifier.padding(end = 8.dp)
        ) {
            Text("开奖历史")
        }
        
        IconButton(
            onClick = onYearSettingClick,
            modifier = Modifier.size(24.dp)
        ) {
            Icon(
                imageVector = Icons.Default.Settings,
                contentDescription = "生肖年份设置"
            )
        }
    }
}

@Composable
fun YearSettingDialog(
    onDismiss: () -> Unit,
    onConfirm: (Int) -> Unit
) {
    val context = LocalContext.current
    // 使用rememberSaveable来保存状态
    var year by rememberSaveable { 
        mutableStateOf(ZodiacUtils.getCurrentBaseYear(context)) 
    }
    var inputText by rememberSaveable { mutableStateOf(year.toString()) }

    AlertDialog(
        onDismissRequest = onDismiss,
        title = { Text("设置生肖年份") },
        text = {
            Column {
                // 只保留文本输入框
                TextField(
                    value = inputText,
                    onValueChange = { 
                        inputText = it
                        it.toIntOrNull()?.let { year = it }
                    },
                    label = { Text("请输入年份") },
                    keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
                    singleLine = true
                )
            }
        },
        confirmButton = {
            TextButton(onClick = { 
                onConfirm(year) 
                // 更新当前年份
                ZodiacUtils.updateBaseYear(context, year)
            }) {
                Text("确定")
            }
        },
        dismissButton = {
            TextButton(onClick = onDismiss) {
                Text("取消")
            }
        }
    )
}

@Preview(showBackground = true)
@Composable
fun MainScreenPreview() {
    MyXuanmazhushouTheme {
        val context = LocalContext.current
        MainScreen(context)
    }
}

// 添加组合生成函数
fun <T> List<T>.combinations(length: Int): List<List<T>> {
    if (length == 0) return listOf(emptyList())
    if (length > size) return emptyList()
    if (length == size) return listOf(this)
    
    return flatMapIndexed { index, item ->
        drop(index + 1).combinations(length - 1).map { listOf(item) + it }
    }
}

// 保持其他函数不变
fun <T> List<T>.sequentialPairs(): List<List<T>> {
    if (size < 2) return emptyList()
    return (0 until size step 2).map { i ->
        if (i + 1 < size) listOf(this[i], this[i + 1])
        else listOf(this[i])
    }
}

fun <T> List<T>.sequentialTriples(): List<List<T>> {
    if (size < 3) return emptyList()
    return (0 until size step 3).map { i ->
        when {
            i + 2 < size -> listOf(this[i], this[i + 1], this[i + 2])
            i + 1 < size -> listOf(this[i], this[i + 1])
            else -> listOf(this[i])
        }
    }
}

// 修改 FlowRow 组件中的 Arrangement 引用
@Composable
fun FlowRow(
    modifier: Modifier = Modifier,
    maxItemsInEachRow: Int = Int.MAX_VALUE,
    horizontalArrangement: Arrangement.Horizontal = Arrangement.Start,
    verticalArrangement: Arrangement.Vertical = Arrangement.Top,
    content: @Composable () -> Unit
) {
    Layout(
        content = content,
        modifier = modifier
    ) { measurables, constraints ->
        val rows = mutableListOf<List<Placeable>>()
        val itemConstraints = constraints.copy(minWidth = 0)
        
        var currentRow = mutableListOf<Placeable>()
        var currentRowWidth = 0
        var currentRowCount = 0
        
        measurables.forEach { measurable ->
            val placeable = measurable.measure(itemConstraints)
            
            if (currentRowCount >= maxItemsInEachRow || currentRowWidth + placeable.width > constraints.maxWidth) {
                rows.add(currentRow)
                currentRow = mutableListOf()
                currentRowWidth = 0
                currentRowCount = 0
            }
            
            currentRow.add(placeable)
            currentRowWidth += placeable.width
            currentRowCount++
        }
        
        if (currentRow.isNotEmpty()) {
            rows.add(currentRow)
        }
        
        val height = rows.sumOf { row ->
            row.maxOfOrNull { it.height } ?: 0
        }
        
        layout(constraints.maxWidth, height) {
            var y = 0
            
            rows.forEach { row ->
                val rowHeight = row.maxOfOrNull { it.height } ?: 0
                val horizontalSpace = constraints.maxWidth - row.sumOf { it.width }
                
                // 简化 spacing 计算逻辑，避免使用 SpaceEvenly 等引用
                val spacing = when {
                    horizontalArrangement == Arrangement.Start -> 0
                    horizontalArrangement == Arrangement.End -> horizontalSpace
                    horizontalArrangement == Arrangement.Center -> horizontalSpace / 2
                    horizontalArrangement.toString().contains("SpaceEvenly") -> horizontalSpace / (row.size + 1)
                    horizontalArrangement.toString().contains("SpaceBetween") -> if (row.size > 1) horizontalSpace / (row.size - 1) else 0
                    horizontalArrangement.toString().contains("SpaceAround") -> horizontalSpace / (row.size * 2)
                    else -> 0
                }
                
                // 简化 x 初始位置计算
                var x = when {
                    horizontalArrangement == Arrangement.Start -> 0
                    horizontalArrangement == Arrangement.End -> horizontalSpace
                    horizontalArrangement == Arrangement.Center -> horizontalSpace / 2
                    horizontalArrangement.toString().contains("SpaceEvenly") -> spacing
                    else -> 0
                }
                
                row.forEach { placeable ->
                    placeable.placeRelative(x, y)
                    
                    // 简化 x 增量计算
                    x += placeable.width + when {
                        horizontalArrangement.toString().contains("SpaceEvenly") -> spacing
                        horizontalArrangement.toString().contains("SpaceBetween") -> spacing
                        horizontalArrangement.toString().contains("SpaceAround") -> spacing * 2
                        else -> 0
                    }
                }
                
                y += rowHeight
            }
        }
    }
}

// 更紧凑的投注格子组件
@Composable
fun CompactBetCell(
    number: String,
    amount: Int,
    onAmountChange: (Int) -> Unit,
    modifier: Modifier = Modifier
) {
    Surface(
        modifier = modifier,
        shape = RoundedCornerShape(4.dp),
        border = BorderStroke(1.dp, Color.LightGray)
    ) {
        Row(
            modifier = Modifier.padding(6.dp),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.spacedBy(4.dp)
        ) {
            // 左侧号码和生肖 - 更紧凑布局
            val num = number.toInt()
            val color = getNumberColor(num)
            val zodiac = ZodiacUtils.getZodiacForNumber(num) ?: ""
            
            // 号码显示
            Surface(
                shape = RoundedCornerShape(4.dp),
                color = color.copy(alpha = 0.1f),
                border = BorderStroke(1.dp, color),
                modifier = Modifier.size(32.dp)
            ) {
                Box(contentAlignment = Alignment.Center) {
                    Column(
                        modifier = Modifier.fillMaxSize(),
                        horizontalAlignment = Alignment.CenterHorizontally,
                        verticalArrangement = Arrangement.Center
                    ) {
                        Text(
                            text = num.toString(),
                            fontSize = 14.sp,
                            fontWeight = FontWeight.Bold,
                            color = color
                        )
                        Text(
                            text = zodiac,
                            fontSize = 10.sp,
                            color = color
                        )
                    }
                }
            }
            
            // 右侧金额输入框 - 最大限度利用空间
            TextField(
                value = if (amount > 0) amount.toString() else "",
                onValueChange = { newValue ->
                    if (newValue.isEmpty() || newValue.all { it.isDigit() }) {
                        onAmountChange(newValue.toIntOrNull() ?: 0)
                    }
                },
                keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
                modifier = Modifier
                    .weight(1f)
                    .height(40.dp)
                    .padding(start = 8.dp),
                singleLine = true,
                textStyle = LocalTextStyle.current.copy(
                    textAlign = TextAlign.Center,
                    fontSize = 14.sp
                ),
                colors = TextFieldDefaults.colors(
                    focusedContainerColor = Color.White,
                    unfocusedContainerColor = Color.White,
                    disabledContainerColor = Color.White,
                    focusedIndicatorColor = Color.Transparent,
                    unfocusedIndicatorColor = Color.Transparent
                )
            )
        }
    }
}

// 添加用于获取号码颜色的函数
fun getNumberColor(number: Int): Color {
    return when {
        number in ZodiacUtils.RED_NUMBERS -> Color.Red
        number in ZodiacUtils.GREEN_NUMBERS -> Color(0xFF228B22) // 绿色
        else -> Color.Blue
    }
}

// 添加一个获取数字各位之和的函数
fun getDigitSum(number: Int): Int {
    return number.toString().sumOf { it.toString().toInt() }
}
