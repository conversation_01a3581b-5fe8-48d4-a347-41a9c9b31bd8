package com.example.myxuanmazhushou

import android.Manifest
import android.app.DatePickerDialog
import android.content.pm.PackageManager
import android.os.Bundle
import android.widget.Toast
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Add
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.DateRange
import androidx.compose.material.icons.filled.Refresh
import androidx.compose.material3.Button
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedTextField
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Switch
import androidx.compose.material3.SwitchDefaults
import androidx.compose.material3.Tab
import androidx.compose.material3.TabRow
import androidx.compose.material3.TabRowDefaults.tabIndicatorOffset
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.material3.TopAppBar
import androidx.compose.material3.TopAppBarDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import androidx.core.content.ContextCompat
import com.example.myxuanmazhushou.stats.ZodiacStatsEngine
import com.example.myxuanmazhushou.utils.ZodiacData
import com.example.myxuanmazhushou.utils.ZodiacUtils
import com.example.myxuanmazhushou.data.DataVersionManager
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import org.json.JSONObject
import java.io.BufferedReader
import java.io.File
import java.io.IOException
import java.io.InputStreamReader
import java.net.URL
import java.util.Calendar

class PredictionActivity : ComponentActivity() {
    private var historicalData = mutableListOf<ZodiacData>()

    // 创建统计引擎实例
    private val statsEngine = ZodiacStatsEngine()

    // 数据版本管理器
    private lateinit var dataVersionManager: DataVersionManager
    
    // 默认保存目录，使用应用内部存储，不需要任何权限
    private val lotteryDataDir by lazy {
        // 使用应用内部存储目录
        val dataDir = File(filesDir, "lottery_data")
        
        // 确保目录存在
        if (!dataDir.exists()) {
            dataDir.mkdirs()
        }
        
        println("调试: 设置历史数据目录为: ${dataDir.absolutePath}")
        dataDir
    }
    
    // 历史数据文件
    private val historyFile by lazy {
        File(lotteryDataDir, "history.csv")
    }
    
    // 对话框显示控制
    private var isShowingDialog = false

    // 权限请求
    private val requestPermissionLauncher = registerForActivityResult(
        ActivityResultContracts.RequestPermission()
    ) { isGranted ->
        if (isGranted) {
            Toast.makeText(this, "已获得存储权限", Toast.LENGTH_SHORT).show()
        } else {
            Toast.makeText(this, "需要存储权限才能保存文件到SD卡", Toast.LENGTH_LONG).show()
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        // 初始化ZodiacUtils
        ZodiacUtils.loadSavedMappings(this)

        // 初始化数据版本管理器
        dataVersionManager = DataVersionManager(this)
        
        setContent {
            MaterialTheme {
                DataManagerScreen()
            }
        }
    }
    
    private fun requestStoragePermission() {
        // Android 10 (Q)及以上版本需要特殊处理
        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.R) {
            try {
                // 对于Android 11+，尝试请求管理所有文件的权限
                val intent = android.content.Intent(android.provider.Settings.ACTION_MANAGE_APP_ALL_FILES_ACCESS_PERMISSION)
                val uri = android.net.Uri.fromParts("package", packageName, null)
                intent.data = uri
                startActivity(intent)
                Toast.makeText(this, "请授予管理所有文件的权限", Toast.LENGTH_LONG).show()
            } catch (e: Exception) {
                Toast.makeText(this, "无法请求文件管理权限: ${e.message}", Toast.LENGTH_LONG).show()
            }
        } else {
            // Android 9及以下版本使用常规权限请求
            if (ContextCompat.checkSelfPermission(
                    this,
                    Manifest.permission.WRITE_EXTERNAL_STORAGE
                ) != PackageManager.PERMISSION_GRANTED
            ) {
                requestPermissionLauncher.launch(Manifest.permission.WRITE_EXTERNAL_STORAGE)
            }
        }
    }

    @OptIn(ExperimentalMaterial3Api::class)
    @Composable
    fun DataManagerScreen() {
        var isLoading by remember { mutableStateOf(true) }  // 默认为加载状态
        var statusMessage by remember { mutableStateOf<String?>(null) }
        var csvFilePath by remember { mutableStateOf<String?>(null) }
        var dataCount by remember { mutableStateOf(historicalData.size) }
        
        // 对话框显示状态
        var showDialog by remember { mutableStateOf(false) }
        
        // 特码生肖统计数据
        var specialZodiacStats by remember { mutableStateOf<Map<String, Int>>(emptyMap()) }
        var specialZodiacByYear by remember { mutableStateOf<Map<String, Map<String, Int>>>(emptyMap()) }
        var specialZodiacByMonth by remember { mutableStateOf<Map<String, Map<String, Int>>>(emptyMap()) }
        
        // 特码号码统计数据
        var specialNumberStats by remember { mutableStateOf<Map<Int, Int>>(emptyMap()) }
        var specialNumberByYear by remember { mutableStateOf<Map<String, Map<Int, Int>>>(emptyMap()) }
        var specialNumberByMonth by remember { mutableStateOf<Map<String, Map<Int, Int>>>(emptyMap()) }
        
        // 统计数据选项卡选择
        var selectedStatTab by remember { mutableStateOf(0) }
        
        // 统计类型选择：true为生肖统计，false为号码统计
        var showZodiacStats by remember { mutableStateOf(false) }
        
        // 更新统计数据的函数
        fun updateStats() {
            // 更新特码生肖统计
            specialZodiacStats = statsEngine.countSpecialZodiacOccurrences(historicalData)
            specialZodiacByYear = statsEngine.countSpecialZodiacByYear(historicalData)
            specialZodiacByMonth = statsEngine.countSpecialZodiacByMonth(historicalData)
            
            // 更新特码号码统计
            specialNumberStats = statsEngine.countSpecialNumberOccurrences(historicalData)
            specialNumberByYear = statsEngine.countSpecialNumberByYear(historicalData)
            specialNumberByMonth = statsEngine.countSpecialNumberByMonth(historicalData)
        }
        
        // 观察isShowingDialog变化
        LaunchedEffect(isShowingDialog) {
            if (isShowingDialog) {
                showDialog = true
                isShowingDialog = false // 重置标志
            }
        }
        
        // 初始化时智能加载数据
        LaunchedEffect(key1 = Unit) {
            isLoading = true
            println("调试: 开始智能数据更新流程")

            // 首先尝试加载本地数据
            val loadResult = loadLatestData()
            println("调试: loadLatestData 结果: $loadResult")

            if (loadResult.startsWith("错误")) {
                // 本地没有数据，进行全量下载
                println("调试: 本地无数据，开始全量下载")
                statusMessage = "正在获取历史数据..."
                val downloadResult = updateData(isFullUpdate = true)
                if (downloadResult.startsWith("错误")) {
                    statusMessage = "数据获取失败: $downloadResult"
                    withContext(Dispatchers.Main) {
                        Toast.makeText(
                            this@PredictionActivity,
                            "数据获取失败，请检查网络连接",
                            Toast.LENGTH_LONG
                        ).show()
                    }
                } else {
                    dataCount = historicalData.size
                    statusMessage = "数据获取成功，共 $dataCount 条记录"
                    // 更新数据版本信息
                    dataVersionManager.updateDataStatus(
                        latestPeriod = if (historicalData.isNotEmpty()) historicalData.first().expect else "",
                        dataCount = dataCount
                    )
                }
            } else {
                // 本地数据存在，检查是否需要更新
                dataCount = historicalData.size
                println("调试: 本地数据加载成功，共 $dataCount 条记录，检查是否需要更新")

                val (needsUpdate, updateMessage) = dataVersionManager.checkForUpdates()
                if (needsUpdate) {
                    println("调试: 数据需要更新，开始增量下载")
                    statusMessage = "正在更新最新数据..."
                    val updateResult = updateData(isFullUpdate = false)
                    if (updateResult.startsWith("错误")) {
                        println("调试: 增量更新失败: $updateResult")
                        statusMessage = "数据已加载 (更新失败)"
                    } else {
                        dataCount = historicalData.size
                        statusMessage = "数据更新成功，共 $dataCount 条记录"
                        // 更新数据版本信息
                        dataVersionManager.updateDataStatus(
                            latestPeriod = if (historicalData.isNotEmpty()) historicalData.first().expect else "",
                            dataCount = dataCount
                        )
                    }
                } else {
                    println("调试: 数据是最新的，无需更新")
                    statusMessage = "数据已是最新，共 $dataCount 条记录"
                }

            }

            // 无论数据来源如何，都要初始化统计数据
            if (historicalData.isNotEmpty()) {
                withContext(Dispatchers.Main) {
                    Toast.makeText(
                        this@PredictionActivity,
                        "正在计算特码统计数据...",
                        Toast.LENGTH_SHORT
                    ).show()
                }

                // 更新统计数据
                println("调试: 开始计算统计数据")
                updateStats()
                println("调试: 统计数据计算完成")

                // 输出样本数据和统计结果
                val sample = historicalData.first()
                println("调试: 样本数据 - 期号: ${sample.expect}, 开奖日期: ${sample.openTime}, 开奖号码: ${sample.openCode}")

                // 检查统计结果
                val nonZeroEntries = specialNumberStats.filter { it.value > 0 }
                println("调试: 统计结果 - 特码非零统计: ${nonZeroEntries.size} 个号码")

                // 显示统计结果提示
                withContext(Dispatchers.Main) {
                    if (nonZeroEntries.isNotEmpty()) {
                        Toast.makeText(
                            this@PredictionActivity,
                            "特码统计完成，有 ${nonZeroEntries.size} 个号码统计结果",
                            Toast.LENGTH_SHORT
                        ).show()
                    } else {
                        Toast.makeText(
                            this@PredictionActivity,
                            "警告：未能解析出有效特码！请查看日志",
                            Toast.LENGTH_LONG
                        ).show()
                    }
                }
            }
            
            isLoading = false
            println("调试: 数据加载过程完成")
        }
    
        Scaffold(
            topBar = {
                TopAppBar(
                    title = { Text("历史数据管理") },
                    navigationIcon = {
                        IconButton(onClick = { finish() }) {
                            Icon(
                                imageVector = Icons.Filled.ArrowBack,
                                contentDescription = "返回"
                            )
                        }
                    },
                    actions = {
                        // 更新按钮
                        IconButton(
                            onClick = {
                                CoroutineScope(Dispatchers.Main).launch {
                                    isLoading = true
                                    
                                    if (historicalData.isEmpty()) {
                                        // 如果本地没有数据，执行全量下载
                                        println("调试: 本地无数据，执行全量下载")
                                        withContext(Dispatchers.Main) {
                                            Toast.makeText(
                                                this@PredictionActivity,
                                                "正在从网络获取全部历史数据...",
                                                Toast.LENGTH_SHORT
                                            ).show()
                                        }
                                        
                                        val result = updateData(isFullUpdate = true)
                                        dataCount = historicalData.size
                                        
                                        if (result.startsWith("错误")) {
                                            statusMessage = result
                                            Toast.makeText(
                                                this@PredictionActivity,
                                                result,
                                                Toast.LENGTH_LONG
                                            ).show()
                                        } else {
                                            statusMessage = "数据更新成功！共获取 $dataCount 条记录"
                                            csvFilePath = result
                                            
                                            // 更新统计数据
                                            updateStats()
                                        }
                                    } else {
                                        // 如果本地有数据，只更新最新一期
                                        println("调试: 本地有数据，只更新最新一期")
                                        withContext(Dispatchers.Main) {
                                            Toast.makeText(
                                                this@PredictionActivity,
                                                "正在获取最新一期数据...",
                                                Toast.LENGTH_SHORT
                                            ).show()
                                        }
                                        
                                        val result = updateData(isFullUpdate = false)
                                        dataCount = historicalData.size
                                        
                                        if (result.startsWith("错误")) {
                                            statusMessage = result
                                            Toast.makeText(
                                                this@PredictionActivity,
                                                result,
                                                Toast.LENGTH_LONG
                                            ).show()
                                        } else {
                                            statusMessage = "数据更新成功！共获取 $dataCount 条记录"
                                            csvFilePath = result
                                            
                                            // 更新统计数据
                                            updateStats()
                                        }
                                    }
                                    
                                    isLoading = false
                                }
                            },
                            enabled = !isLoading
                        ) {
                            Icon(
                                imageVector = Icons.Filled.Refresh,
                                contentDescription = "更新",
                                tint = MaterialTheme.colorScheme.onPrimary
                            )
                        }
                        
                        // 新增按钮
                        IconButton(
                            onClick = { showDialog = true },
                            enabled = !isLoading
                        ) {
                            Icon(
                                imageVector = Icons.Filled.Add,
                                contentDescription = "新增",
                                tint = MaterialTheme.colorScheme.onPrimary
                            )
                        }
                    },
                    colors = TopAppBarDefaults.topAppBarColors(
                        containerColor = MaterialTheme.colorScheme.primary,
                        titleContentColor = MaterialTheme.colorScheme.onPrimary,
                        navigationIconContentColor = MaterialTheme.colorScheme.onPrimary
                    )
                )
            }
        ) { paddingValues ->
            LazyColumn(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(paddingValues)
                    .padding(16.dp),
                verticalArrangement = Arrangement.spacedBy(16.dp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                // 历史数据管理工具卡片 - 移除
                
                // 特码统计卡片
                item {
                    // 始终显示统计卡片，如果没有数据则显示提示
                    if (!isLoading) {
                        Card(
                            modifier = Modifier.fillMaxWidth(),
                            shape = RoundedCornerShape(12.dp),
                            elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
                        ) {
                            Column(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .padding(16.dp)
                            ) {
                                // 卡片标题行
                                Row(
                                    modifier = Modifier.fillMaxWidth(),
                                    horizontalArrangement = Arrangement.SpaceBetween,
                                    verticalAlignment = Alignment.CenterVertically
                                ) {
                                    Column {
                                        // 主标题
                                        Text(
                                            text = "特码号码分析",
                                            fontSize = 20.sp,
                                            fontWeight = FontWeight.Bold,
                                            color = MaterialTheme.colorScheme.primary,
                                            modifier = Modifier.padding(bottom = 2.dp)
                                        )
                                        
                                        // 显示记录数
                                        Text(
                                            text = "当前加载记录数: $dataCount",
                                            fontSize = 12.sp,
                                            color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f)
                                        )
                                    }
                                    
                                    // 切换按钮：生肖/号码统计
                                    Row(
                                        verticalAlignment = Alignment.CenterVertically,
                                        modifier = Modifier
                                            .background(
                                                color = MaterialTheme.colorScheme.surfaceVariant,
                                                shape = RoundedCornerShape(20.dp)
                                            )
                                            .padding(4.dp)
                                    ) {
                                        Text(
                                            text = "号码统计",
                                            fontSize = 14.sp,
                                            fontWeight = if (!showZodiacStats) FontWeight.Bold else FontWeight.Normal,
                                            color = if (!showZodiacStats) 
                                                MaterialTheme.colorScheme.primary 
                                            else 
                                                MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f),
                                            modifier = Modifier.padding(horizontal = 8.dp, vertical = 4.dp)
                                        )
                                        
                                        Switch(
                                            checked = showZodiacStats,
                                            onCheckedChange = { showZodiacStats = it },
                                            modifier = Modifier.size(40.dp, 20.dp),
                                            colors = SwitchDefaults.colors(
                                                checkedThumbColor = MaterialTheme.colorScheme.primary,
                                                checkedTrackColor = MaterialTheme.colorScheme.primaryContainer,
                                                uncheckedThumbColor = MaterialTheme.colorScheme.primary,
                                                uncheckedTrackColor = MaterialTheme.colorScheme.surfaceVariant
                                            )
                                        )
                                        
                                        Text(
                                            text = "生肖统计",
                                            fontSize = 14.sp,
                                            fontWeight = if (showZodiacStats) FontWeight.Bold else FontWeight.Normal,
                                            color = if (showZodiacStats) 
                                                MaterialTheme.colorScheme.primary 
                                            else 
                                                MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f),
                                            modifier = Modifier.padding(horizontal = 8.dp, vertical = 4.dp)
                                        )
                                    }
                                }
                                
                                Spacer(modifier = Modifier.height(16.dp))
                                
                                // 如果有数据则显示统计信息，否则显示提示
                                if (historicalData.isNotEmpty()) {
                                    // 选项卡切换不同的统计视图
                                    TabRow(
                                        selectedTabIndex = selectedStatTab,
                                        modifier = Modifier
                                            .fillMaxWidth()
                                            .clip(RoundedCornerShape(12.dp)),
                                        containerColor = MaterialTheme.colorScheme.surfaceVariant,
                                        indicator = { tabPositions ->
                                            Box(
                                                modifier = Modifier
                                                    .tabIndicatorOffset(tabPositions[selectedStatTab])
                                                    .height(3.dp)
                                                    .background(
                                                        color = MaterialTheme.colorScheme.primary,
                                                        shape = RoundedCornerShape(topStart = 3.dp, topEnd = 3.dp)
                                                    )
                                            )
                                        }
                                    ) {
                                        Tab(
                                            selected = selectedStatTab == 0,
                                            onClick = { selectedStatTab = 0 },
                                            text = { Text("总体统计") }
                                        )
                                        Tab(
                                            selected = selectedStatTab == 1,
                                            onClick = { selectedStatTab = 1 },
                                            text = { Text("年度统计") }
                                        )
                                        Tab(
                                            selected = selectedStatTab == 2,
                                            onClick = { selectedStatTab = 2 },
                                            text = { Text("月度统计") }
                                        )
                                    }
                                    
                                    Spacer(modifier = Modifier.height(16.dp))
                                    
                                    // 根据选择的选项卡和统计类型显示不同的统计数据
                                    if (showZodiacStats) {
                                        // 显示生肖统计
                                        when (selectedStatTab) {
                                            0 -> TotalStatView(specialZodiacStats)
                                            1 -> YearlyStatView(specialZodiacByYear)
                                            2 -> MonthlyStatView(specialZodiacByMonth)
                                        }
                                    } else {
                                        // 显示号码统计
                                        when (selectedStatTab) {
                                            0 -> TotalNumberStatView(specialNumberStats)
                                            1 -> YearlyNumberStatView(specialNumberByYear)
                                            2 -> MonthlyNumberStatView(specialNumberByMonth)
                                        }
                                    }
                                } else {
                                    // 显示提示加载数据信息
                                    Box(
                                        modifier = Modifier
                                            .fillMaxWidth()
                                            .height(200.dp)
                                            .background(
                                                color = MaterialTheme.colorScheme.surfaceVariant.copy(alpha = 0.5f),
                                                shape = RoundedCornerShape(8.dp)
                                            ),
                                        contentAlignment = Alignment.Center
                                    ) {
                                        Column(
                                            horizontalAlignment = Alignment.CenterHorizontally,
                                            verticalArrangement = Arrangement.Center
                                        ) {
                                            Icon(
                                                imageVector = Icons.Filled.Refresh,
                                                contentDescription = null,
                                                tint = MaterialTheme.colorScheme.primary,
                                                modifier = Modifier.size(48.dp)
                                            )
                                            
                                            Spacer(modifier = Modifier.height(16.dp))
                                            
                                            Text(
                                                text = "请点击顶部刷新按钮加载数据",
                                                fontSize = 16.sp,
                                                fontWeight = FontWeight.Medium
                                            )
                                        }
                                    }
                                }
                            }
                        }
                    } else {
                        // 显示加载中状态
                        Card(
                            modifier = Modifier.fillMaxWidth(),
                            shape = RoundedCornerShape(12.dp),
                            elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
                        ) {
                            Box(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .height(300.dp)
                                    .padding(16.dp),
                                contentAlignment = Alignment.Center
                            ) {
                                Column(
                                    horizontalAlignment = Alignment.CenterHorizontally,
                                    verticalArrangement = Arrangement.Center
                                ) {
                                    CircularProgressIndicator(
                                        modifier = Modifier.size(48.dp),
                                        color = MaterialTheme.colorScheme.primary
                                    )
                                    
                                    Spacer(modifier = Modifier.height(16.dp))
                                    
                                    Text(
                                        text = "正在加载数据，请稍候...",
                                        fontSize = 16.sp,
                                        fontWeight = FontWeight.Medium
                                    )
                                }
                            }
                        }
                    }
                }
            }
            
            // 显示添加数据对话框
            if (showDialog) {
                AddLotteryDataDialog(
                    onDismiss = { showDialog = false },
                    onAdd = { expect, openTime, openCode ->
                        // 添加新数据的逻辑
                        addNewLotteryData(expect, openTime, openCode)
                        // 更新UI显示
                        dataCount = historicalData.size
                        // 更新统计数据
                        updateStats()
                        showDialog = false
                    }
                )
            }
        }
    }
    
    @OptIn(ExperimentalMaterial3Api::class)
    @Composable
    private fun AddLotteryDataDialog(
        onDismiss: () -> Unit,
        onAdd: (expect: String, openTime: String, openCode: String) -> Unit
    ) {
        val context = LocalContext.current
        
        // 计算下一期期号（最新一期+1）
        val nextExpect = remember {
            if (historicalData.isEmpty()) {
                "2023001" // 默认起始期号
            } else {
                // 假设期号格式为年份+序号
                val latestExpect = historicalData.maxByOrNull { it.expect }?.expect ?: "2023000"
                val year = latestExpect.take(4)
                val numStr = latestExpect.drop(4)
                val num = numStr.toIntOrNull() ?: 0
                String.format("%s%03d", year, num + 1)
            }
        }
        
        var expect by remember { mutableStateOf(nextExpect) }
        var openTime by remember { mutableStateOf("") }
        var openCode by remember { mutableStateOf("") } // 标准格式的开奖码
        
        // 设置默认日期为今天
        LaunchedEffect(Unit) {
            val calendar = Calendar.getInstance()
            val year = calendar.get(Calendar.YEAR)
            val month = calendar.get(Calendar.MONTH) + 1
            val day = calendar.get(Calendar.DAY_OF_MONTH)
            openTime = String.format("%04d-%02d-%02d", year, month, day)
        }
        
        Dialog(onDismissRequest = onDismiss) {
            Card(
                shape = RoundedCornerShape(16.dp),
                elevation = CardDefaults.cardElevation(defaultElevation = 8.dp),
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(16.dp)
            ) {
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(16.dp),
                    verticalArrangement = Arrangement.spacedBy(12.dp)
                ) {
                    // 标题
                    Text(
                        text = "添加新的开奖数据",
                        fontSize = 20.sp,
                        fontWeight = FontWeight.Bold,
                        modifier = Modifier.padding(vertical = 8.dp)
                    )
                    
                    // 期号输入（自动填充，但允许修改）
                    OutlinedTextField(
                        value = expect,
                        onValueChange = { expect = it },
                        label = { Text("期号 (自动设为最新一期+1)") },
                        modifier = Modifier.fillMaxWidth(),
                        singleLine = true
                    )
                    
                    // 开奖日期选择
                    OutlinedTextField(
                        value = openTime,
                        onValueChange = { openTime = it },
                        label = { Text("开奖日期") },
                        modifier = Modifier.fillMaxWidth(),
                        singleLine = true,
                        readOnly = true,
                        trailingIcon = {
                            IconButton(
                                onClick = {
                                    val calendar = Calendar.getInstance()
                                    val year = calendar.get(Calendar.YEAR)
                                    val month = calendar.get(Calendar.MONTH)
                                    val day = calendar.get(Calendar.DAY_OF_MONTH)
                                    
                                    val datePickerDialog = DatePickerDialog(
                                        context,
                                        { _, selectedYear, selectedMonth, selectedDay ->
                                            val formattedDate = String.format(
                                                "%04d-%02d-%02d", 
                                                selectedYear, 
                                                selectedMonth + 1, 
                                                selectedDay
                                            )
                                            openTime = formattedDate
                                        },
                                        year, month, day
                                    )
                                    datePickerDialog.show()
                                }
                            ) {
                                Icon(
                                    imageVector = Icons.Filled.DateRange,
                                    contentDescription = "选择日期"
                                )
                            }
                        }
                    )
                    
                    // 开奖号码输入（标准格式）
                    OutlinedTextField(
                        value = openCode,
                        onValueChange = { openCode = it },
                        label = { Text("开奖号码") },
                        placeholder = { Text("例如: 12,34,56,07,08,09+10") },
                        modifier = Modifier.fillMaxWidth(),
                        singleLine = true
                    )
                    
                    // 提示信息
                    Text(
                        text = "注意: 请输入标准格式，用英文逗号分隔前区6个号码，加号分隔特别号码",
                        fontSize = 12.sp,
                        color = MaterialTheme.colorScheme.secondary,
                        modifier = Modifier.padding(top = 4.dp)
                    )
                    
                    // 按钮行
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(top = 16.dp),
                        horizontalArrangement = Arrangement.End
                    ) {
                        // 取消按钮
                        TextButton(
                            onClick = onDismiss
                        ) {
                            Text("取消")
                        }
                        
                        // 添加按钮
                        Button(
                            onClick = {
                                if (expect.isNotBlank() && openTime.isNotBlank() && openCode.isNotBlank()) {
                                    // 验证开奖码格式
                                    if (verifyOpenCodeFormat(openCode)) {
                                        onAdd(expect, openTime, openCode)
                                    } else {
                                        Toast.makeText(context, "开奖号码格式错误，请使用正确格式：前区6个号码用逗号分隔，加号分隔特别号码", Toast.LENGTH_LONG).show()
                                    }
                                } else {
                                    Toast.makeText(context, "所有字段都必须填写", Toast.LENGTH_SHORT).show()
                                }
                            }
                        ) {
                            Text("添加")
                        }
                    }
                }
            }
        }
    }
    
    @Composable
    fun TotalStatView(stats: Map<String, Int>) {
        if (stats.isEmpty()) {
            Text("暂无生肖统计数据")
            return
        }
        
        // 查找最大和最小出现次数，用于直观展示
        val maxCount = stats.values.maxOrNull() ?: 0
        val minCount = stats.values.minOrNull() ?: 0
        
        Column(modifier = Modifier.fillMaxWidth()) {
            Text(
                text = "特码生肖总体出现次数统计",
                fontWeight = FontWeight.Bold,
                modifier = Modifier.padding(vertical = 8.dp)
            )
            
            // 显示生肖统计数据
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(vertical = 4.dp),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Text("生肖", fontWeight = FontWeight.Bold, modifier = Modifier.width(48.dp))
                Text("次数", fontWeight = FontWeight.Bold, modifier = Modifier.width(48.dp))
                Text("比例", fontWeight = FontWeight.Bold, modifier = Modifier.width(64.dp))
            }
            
            // 按出现次数从大到小排序
            val sortedStats = statsEngine.zodiacList
                .map { zodiac -> zodiac to (stats[zodiac] ?: 0) }
                .sortedByDescending { (_, count) -> count }
            
            sortedStats.forEach { (zodiac, count) ->
                val total = stats.values.sum()
                val percentage = if (total > 0) (count.toFloat() / total * 100) else 0f
                
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(vertical = 4.dp),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(zodiac, modifier = Modifier.width(48.dp))
                    Text("$count", modifier = Modifier.width(48.dp))
                    Text(
                        String.format("%.1f%%", percentage),
                        modifier = Modifier.width(64.dp)
                    )
                    
                    // 添加可视化进度条
                    if (maxCount > 0) {
                        val barWidth = (count.toFloat() / maxCount) * 100
                        Box(
                            modifier = Modifier
                                .weight(1f)
                                .height(16.dp)
                                .background(Color.LightGray)
                        ) {
                            Box(
                                modifier = Modifier
                                    .fillMaxHeight()
                                    .width((barWidth * 1.2).dp) // 稍微放大一些进度条便于观察
                                    .background(MaterialTheme.colorScheme.primary.copy(alpha = 0.7f))
                            )
                        }
                    }
                }
            }
        }
    }
    
    @Composable
    fun YearlyStatView(yearlyStats: Map<String, Map<String, Int>>) {
        if (yearlyStats.isEmpty()) {
            Text("暂无年度统计数据")
            return
        }
        
        Column(modifier = Modifier.fillMaxWidth()) {
            Text(
                text = "特码生肖年度出现次数统计",
                fontWeight = FontWeight.Bold,
                modifier = Modifier.padding(vertical = 8.dp)
            )
            
            // 按年份降序排序
            val sortedYears = yearlyStats.keys.sortedByDescending { it }
            
            sortedYears.forEach { year ->
                val yearData = yearlyStats[year] ?: return@forEach
                val totalInYear = yearData.values.sum()
                
                Text(
                    text = "$year 年 (共${totalInYear}期)",
                    fontWeight = FontWeight.Bold,
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(top = 12.dp, bottom = 4.dp)
                )
                
                // 生肖统计表格
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .border(1.dp, Color.LightGray)
                        .padding(8.dp)
                ) {
                    // 按出现次数从大到小排序生肖
                    val sortedZodiacs = statsEngine.zodiacList
                        .map { zodiac -> zodiac to (yearData[zodiac] ?: 0) }
                        .sortedByDescending { (_, count) -> count }
                        .chunked(4)
                    
                    sortedZodiacs.forEach { rowZodiacs ->
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(vertical = 4.dp),
                            horizontalArrangement = Arrangement.SpaceBetween
                        ) {
                            rowZodiacs.forEach { (zodiac, count) ->
                                val percentage = if (totalInYear > 0) 
                                    (count.toFloat() / totalInYear * 100) else 0f
                                
                                Column(
                                    modifier = Modifier.weight(1f),
                                    horizontalAlignment = Alignment.CenterHorizontally
                                ) {
                                    Text(zodiac, fontWeight = FontWeight.Bold)
                                    Text("$count (${String.format("%.1f%%", percentage)})")
                                }
                            }
                        }
                    }
                }
            }
        }
    }
    
    @Composable
    fun MonthlyStatView(monthlyStats: Map<String, Map<String, Int>>) {
        if (monthlyStats.isEmpty()) {
            Text("暂无月度统计数据")
            return
        }
        
        Column(modifier = Modifier.fillMaxWidth()) {
            Text(
                text = "特码生肖月度出现次数统计（最近12个月）",
                fontWeight = FontWeight.Bold,
                modifier = Modifier.padding(vertical = 8.dp)
            )
            
            // 按月份降序排序并只取最近12个月
            val sortedMonths = monthlyStats.keys
                .sortedByDescending { it }
                .take(12)
            
            sortedMonths.forEach { month ->
                val monthData = monthlyStats[month] ?: return@forEach
                val totalInMonth = monthData.values.sum()
                
                if (totalInMonth > 0) {
                    Text(
                        text = "$month (共${totalInMonth}期)",
                        fontWeight = FontWeight.Bold,
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(top = 12.dp, bottom = 4.dp)
                    )
                    
                    // 生肖统计数据表格
                    Column(
                        modifier = Modifier
                            .fillMaxWidth()
                            .border(1.dp, Color.LightGray)
                            .padding(8.dp)
                    ) {
                        // 按出现次数从大到小排序生肖
                        val sortedZodiacs = statsEngine.zodiacList
                            .map { zodiac -> zodiac to (monthData[zodiac] ?: 0) }
                            .sortedByDescending { (_, count) -> count }
                            .chunked(4)
                        
                        sortedZodiacs.forEach { rowZodiacs ->
                            Row(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .padding(vertical = 4.dp),
                                horizontalArrangement = Arrangement.SpaceBetween
                            ) {
                                rowZodiacs.forEach { (zodiac, count) ->
                                    val percentage = if (totalInMonth > 0) 
                                        (count.toFloat() / totalInMonth * 100) else 0f
                                    
                                    Column(
                                        modifier = Modifier.weight(1f),
                                        horizontalAlignment = Alignment.CenterHorizontally
                                    ) {
                                        Text(zodiac, fontWeight = FontWeight.Bold)
                                        Text("$count (${String.format("%.1f%%", percentage)})")
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    @Composable
    fun TotalNumberStatView(totalStats: Map<Int, Int>) {
        if (totalStats.isEmpty()) {
            Text("暂无特码号码统计数据")
            return
        }
        
        Column(modifier = Modifier.fillMaxWidth()) {
            Text(
                text = "特码号码总体统计",
                fontWeight = FontWeight.Bold,
                fontSize = 16.sp,
                modifier = Modifier.padding(vertical = 8.dp)
            )
            
            val totalOccurrences = totalStats.values.sum().toFloat()
            
            // 所有号码按出现次数从大到小排序，不分波色
            val sortedAllNumbers = totalStats.entries
                .filter { it.value > 0 }  // 只显示有出现次数的号码
                .sortedByDescending { it.value }
                .map { it.key to it.value }
            
            // 显示所有号码，按出现次数排序
            Text(
                text = "所有号码（按出现次数排序）",
                fontWeight = FontWeight.Medium,
                fontSize = 14.sp,
                color = MaterialTheme.colorScheme.primary,
                modifier = Modifier.padding(vertical = 8.dp)
            )
            
            // 显示所有号码
            Column(verticalArrangement = Arrangement.spacedBy(8.dp)) {
                sortedAllNumbers.forEach { (number, count) ->
                    val percentage = if (totalOccurrences > 0) count / totalOccurrences else 0f
                    NumberBox(
                        number = number,
                        count = count,
                        percentage = percentage,
                        modifier = Modifier.fillMaxWidth()
                    )
                }
            }
            
            Spacer(modifier = Modifier.height(24.dp))
            
            // 按波色分组的标题
            Text(
                text = "按波色分组",
                fontWeight = FontWeight.Medium,
                fontSize = 14.sp,
                color = MaterialTheme.colorScheme.primary,
                modifier = Modifier.padding(vertical = 8.dp)
            )
            
            // 按波色分组，并在每个波色内部按出现次数从大到小排序
            val sortedRedNumbers = ZodiacUtils.RED_NUMBERS
                .map { number -> number to (totalStats.getOrDefault(number, 0)) }
                .sortedByDescending { (_, count) -> count }
            
            val sortedBlueNumbers = ZodiacUtils.BLUE_NUMBERS
                .map { number -> number to (totalStats.getOrDefault(number, 0)) }
                .sortedByDescending { (_, count) -> count }
            
            val sortedGreenNumbers = ZodiacUtils.GREEN_NUMBERS
                .map { number -> number to (totalStats.getOrDefault(number, 0)) }
                .sortedByDescending { (_, count) -> count }
            
            // 显示红波号码
            Text(
                text = "红波",
                fontWeight = FontWeight.Medium,
                fontSize = 14.sp,
                color = Color(0xFFE57373),
                modifier = Modifier.padding(vertical = 4.dp)
            )
            
            // 显示红波号码
            Column(verticalArrangement = Arrangement.spacedBy(8.dp)) {
                sortedRedNumbers.forEach { (number, count) ->
                    val percentage = if (totalOccurrences > 0) count / totalOccurrences else 0f
                    NumberBox(
                        number = number,
                        count = count,
                        percentage = percentage,
                        modifier = Modifier.fillMaxWidth()
                    )
                }
            }
            
            Spacer(modifier = Modifier.height(16.dp))
            
            Text(
                text = "蓝波",
                fontWeight = FontWeight.Medium,
                fontSize = 14.sp,
                color = Color(0xFF64B5F6),
                modifier = Modifier.padding(vertical = 4.dp)
            )
            
            // 显示蓝波号码
            Column(verticalArrangement = Arrangement.spacedBy(8.dp)) {
                sortedBlueNumbers.forEach { (number, count) ->
                    val percentage = if (totalOccurrences > 0) count / totalOccurrences else 0f
                    NumberBox(
                        number = number,
                        count = count,
                        percentage = percentage,
                        modifier = Modifier.fillMaxWidth()
                    )
                }
            }
            
            Spacer(modifier = Modifier.height(16.dp))
            
            Text(
                text = "绿波",
                fontWeight = FontWeight.Medium,
                fontSize = 14.sp,
                color = Color(0xFF81C784),
                modifier = Modifier.padding(vertical = 4.dp)
            )
            
            // 显示绿波号码
            Column(verticalArrangement = Arrangement.spacedBy(8.dp)) {
                sortedGreenNumbers.forEach { (number, count) ->
                    val percentage = if (totalOccurrences > 0) count / totalOccurrences else 0f
                    NumberBox(
                        number = number,
                        count = count,
                        percentage = percentage,
                        modifier = Modifier.fillMaxWidth()
                    )
                }
            }
        }
    }
    
    @Composable
    fun YearlyNumberStatView(yearlyStats: Map<String, Map<Int, Int>>) {
        if (yearlyStats.isEmpty()) {
            Text("暂无年度号码统计数据")
            return
        }
        
        Column(modifier = Modifier.fillMaxWidth()) {
            Text(
                text = "特码号码年度统计",
                fontWeight = FontWeight.Bold,
                fontSize = 16.sp,
                modifier = Modifier.padding(vertical = 8.dp)
            )
            
            // 安全处理数据
            val safeYearlyStats = yearlyStats
            
            // 按年份降序排序
            val sortedYears = safeYearlyStats.keys.toList().sortedByDescending { it }
            
            if (sortedYears.isEmpty()) {
                Text("无可用的年度统计数据")
                return@Column
            }
            
            // 显示年份统计数据
            sortedYears.forEach { year ->
                val yearData = safeYearlyStats[year] ?: return@forEach
                
                // 安全计算总数
                val totalInYearData = yearData.values.sum()
                
                // 获取所有开出过的号码（count > 0），并按出现次数从大到小排序
                val allOpenedNumbers = yearData.entries
                    .filter { it.value > 0 }
                    .sortedByDescending { it.value }
                    .map { it.key to it.value }
                
                // 获取这一年开出过的号码（count > 0），并按出现次数从大到小排序
                val openedRedNumbers = ZodiacUtils.RED_NUMBERS
                    .filter { number -> yearData.getOrDefault(number, 0) > 0 }
                    .map { number -> number to yearData.getOrDefault(number, 0) }
                    .sortedByDescending { (_, count) -> count }
                    .map { it.first }
                
                val openedBlueNumbers = ZodiacUtils.BLUE_NUMBERS
                    .filter { number -> yearData.getOrDefault(number, 0) > 0 }
                    .map { number -> number to yearData.getOrDefault(number, 0) }
                    .sortedByDescending { (_, count) -> count }
                    .map { it.first }
                
                val openedGreenNumbers = ZodiacUtils.GREEN_NUMBERS
                    .filter { number -> yearData.getOrDefault(number, 0) > 0 }
                    .map { number -> number to yearData.getOrDefault(number, 0) }
                    .sortedByDescending { (_, count) -> count }
                    .map { it.first }
                
                // 只有当有任意号码开出时才显示年份卡片
                if (allOpenedNumbers.isNotEmpty()) {
                    Card(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(vertical = 8.dp),
                        colors = CardDefaults.cardColors(containerColor = MaterialTheme.colorScheme.surfaceVariant.copy(alpha = 0.7f)),
                        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
                    ) {
                        Column(modifier = Modifier.padding(16.dp)) {
                            Text(
                                text = "$year 年（共${totalInYearData}期）",
                                fontWeight = FontWeight.Bold,
                                fontSize = 16.sp,
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .padding(vertical = 12.dp, horizontal = 16.dp)
                            )
                            
                            // 显示所有号码，按出现次数排序
                            Text(
                                text = "所有号码（按出现次数排序）",
                                fontWeight = FontWeight.Medium,
                                fontSize = 14.sp,
                                color = MaterialTheme.colorScheme.primary,
                                modifier = Modifier.padding(vertical = 8.dp)
                            )
                            
                            // 显示所有开出过的号码
                            Column(verticalArrangement = Arrangement.spacedBy(8.dp)) {
                                allOpenedNumbers.forEach { (number, count) ->
                                    val percentage = if (totalInYearData > 0) count.toFloat() / totalInYearData else 0f
                                    NumberBox(
                                        number = number,
                                        count = count,
                                        percentage = percentage,
                                        modifier = Modifier.fillMaxWidth()
                                    )
                                }
                            }
                            
                            Spacer(modifier = Modifier.height(24.dp))
                            
                            // 按波色分组的标题
                            Text(
                                text = "按波色分组",
                                fontWeight = FontWeight.Medium,
                                fontSize = 14.sp,
                                color = MaterialTheme.colorScheme.primary,
                                modifier = Modifier.padding(vertical = 8.dp)
                            )
                            
                            // 只有当有红波号码开出时才显示红波分组
                            if (openedRedNumbers.isNotEmpty()) {
                                Text(
                                    text = "红波",
                                    fontWeight = FontWeight.Medium,
                                    fontSize = 14.sp,
                                    color = Color(0xFFE57373),
                                    modifier = Modifier.padding(vertical = 4.dp)
                                )
                                
                                // 只显示开出过的红波号码
                                Column(verticalArrangement = Arrangement.spacedBy(8.dp)) {
                                    openedRedNumbers.forEach { number ->
                                        val count = yearData.getOrDefault(number, 0)
                                        val percentage = if (totalInYearData > 0) count.toFloat() / totalInYearData else 0f
                                        NumberBox(
                                            number = number,
                                            count = count,
                                            percentage = percentage,
                                            modifier = Modifier.fillMaxWidth()
                                        )
                                    }
                                }
                                
                                Spacer(modifier = Modifier.height(16.dp))
                            }
                            
                            // 只有当有蓝波号码开出时才显示蓝波分组
                            if (openedBlueNumbers.isNotEmpty()) {
                                Text(
                                    text = "蓝波",
                                    fontWeight = FontWeight.Medium,
                                    fontSize = 14.sp,
                                    color = Color(0xFF64B5F6),
                                    modifier = Modifier.padding(vertical = 4.dp)
                                )
                                
                                // 只显示开出过的蓝波号码
                                Column(verticalArrangement = Arrangement.spacedBy(8.dp)) {
                                    openedBlueNumbers.forEach { number ->
                                        val count = yearData.getOrDefault(number, 0)
                                        val percentage = if (totalInYearData > 0) count.toFloat() / totalInYearData else 0f
                                        NumberBox(
                                            number = number,
                                            count = count,
                                            percentage = percentage,
                                            modifier = Modifier.fillMaxWidth()
                                        )
                                    }
                                }
                            }
                            
                            // 只有当有绿波号码开出时才显示绿波分组
                            if (openedGreenNumbers.isNotEmpty()) {
                                Text(
                                    text = "绿波",
                                    fontWeight = FontWeight.Medium,
                                    fontSize = 14.sp,
                                    color = Color(0xFF81C784),
                                    modifier = Modifier.padding(vertical = 4.dp)
                                )
                                
                                // 只显示开出过的绿波号码
                                Column(verticalArrangement = Arrangement.spacedBy(8.dp)) {
                                    openedGreenNumbers.forEach { number ->
                                        val count = yearData.getOrDefault(number, 0)
                                        val percentage = if (totalInYearData > 0) count.toFloat() / totalInYearData else 0f
                                        NumberBox(
                                            number = number,
                                            count = count,
                                            percentage = percentage,
                                            modifier = Modifier.fillMaxWidth()
                                        )
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }
    
    @Composable
    fun MonthlyNumberStatView(monthlyStats: Map<String, Map<Int, Int>>) {
        if (monthlyStats.isEmpty()) {
            Text("暂无月度号码统计数据")
            return
        }
        
        Column(modifier = Modifier.fillMaxWidth()) {
            Text(
                text = "特码号码月度统计",
                fontWeight = FontWeight.Bold,
                fontSize = 16.sp,
                modifier = Modifier.padding(vertical = 8.dp)
            )
            
            // 安全处理数据
            val safeMonthlyStats = monthlyStats
            
            // 按年月降序排序，取最近12个月
            val sortedMonths = safeMonthlyStats.keys.toList().sortedByDescending { it }.take(12)
            
            if (sortedMonths.isEmpty()) {
                Text("无可用的月度统计数据")
                return@Column
            }
            
            // 显示月份统计数据
            sortedMonths.forEach { month ->
                val monthData = safeMonthlyStats[month] ?: return@forEach
                
                // 获取所有开出过的号码（count > 0），并按出现次数从大到小排序
                val allOpenedNumbers = monthData.entries
                    .filter { it.value > 0 }
                    .sortedByDescending { it.value }
                    .map { it.key to it.value }
                
                // 获取这个月开出过的号码（count > 0），并按出现次数从大到小排序
                val openedRedNumbers = ZodiacUtils.RED_NUMBERS
                    .filter { number -> monthData.getOrDefault(number, 0) > 0 }
                    .map { number -> number to monthData.getOrDefault(number, 0) }
                    .sortedByDescending { (_, count) -> count }
                    .map { it.first }
                
                val openedBlueNumbers = ZodiacUtils.BLUE_NUMBERS
                    .filter { number -> monthData.getOrDefault(number, 0) > 0 }
                    .map { number -> number to monthData.getOrDefault(number, 0) }
                    .sortedByDescending { (_, count) -> count }
                    .map { it.first }
                
                val openedGreenNumbers = ZodiacUtils.GREEN_NUMBERS
                    .filter { number -> monthData.getOrDefault(number, 0) > 0 }
                    .map { number -> number to monthData.getOrDefault(number, 0) }
                    .sortedByDescending { (_, count) -> count }
                    .map { it.first }
                
                // 只有当有任意号码开出时才显示月份卡片
                if (allOpenedNumbers.isNotEmpty()) {
                    Card(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(vertical = 8.dp),
                        colors = CardDefaults.cardColors(containerColor = MaterialTheme.colorScheme.surfaceVariant.copy(alpha = 0.7f)),
                        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
                    ) {
                        Column(modifier = Modifier.padding(16.dp)) {
                            // 安全计算总数
                            val totalInMonthData = monthData.values.sum()
                            
                            Text(
                                text = "$month（共${totalInMonthData}期）",
                                fontWeight = FontWeight.Bold,
                                fontSize = 16.sp,
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .padding(vertical = 12.dp, horizontal = 16.dp)
                            )
                            
                            // 显示所有号码，按出现次数排序
                            Text(
                                text = "所有号码（按出现次数排序）",
                                fontWeight = FontWeight.Medium,
                                fontSize = 14.sp,
                                color = MaterialTheme.colorScheme.primary,
                                modifier = Modifier.padding(vertical = 8.dp)
                            )
                            
                            // 显示所有开出过的号码
                            Column(verticalArrangement = Arrangement.spacedBy(8.dp)) {
                                allOpenedNumbers.forEach { (number, count) ->
                                    val percentage = if (totalInMonthData > 0) count.toFloat() / totalInMonthData else 0f
                                    NumberBox(
                                        number = number,
                                        count = count,
                                        percentage = percentage,
                                        modifier = Modifier.fillMaxWidth()
                                    )
                                }
                            }
                            
                            Spacer(modifier = Modifier.height(24.dp))
                            
                            // 按波色分组的标题
                            Text(
                                text = "按波色分组",
                                fontWeight = FontWeight.Medium,
                                fontSize = 14.sp,
                                color = MaterialTheme.colorScheme.primary,
                                modifier = Modifier.padding(vertical = 8.dp)
                            )
                            
                            // 只有当有红波号码开出时才显示红波分组
                            if (openedRedNumbers.isNotEmpty()) {
                                Text(
                                    text = "红波",
                                    fontWeight = FontWeight.Medium,
                                    fontSize = 14.sp,
                                    color = Color(0xFFE57373),
                                    modifier = Modifier.padding(vertical = 4.dp)
                                )
                                
                                // 只显示开出过的红波号码
                                Column(verticalArrangement = Arrangement.spacedBy(8.dp)) {
                                    openedRedNumbers.forEach { number ->
                                        val count = monthData.getOrDefault(number, 0)
                                        val percentage = if (totalInMonthData > 0) count.toFloat() / totalInMonthData else 0f
                                        NumberBox(
                                            number = number,
                                            count = count,
                                            percentage = percentage,
                                            modifier = Modifier.fillMaxWidth()
                                        )
                                    }
                                }
                                
                                Spacer(modifier = Modifier.height(16.dp))
                            }
                            
                            // 只有当有蓝波号码开出时才显示蓝波分组
                            if (openedBlueNumbers.isNotEmpty()) {
                                Text(
                                    text = "蓝波",
                                    fontWeight = FontWeight.Medium,
                                    fontSize = 14.sp,
                                    color = Color(0xFF64B5F6),
                                    modifier = Modifier.padding(vertical = 4.dp)
                                )
                                
                                // 只显示开出过的蓝波号码
                                Column(verticalArrangement = Arrangement.spacedBy(8.dp)) {
                                    openedBlueNumbers.forEach { number ->
                                        val count = monthData.getOrDefault(number, 0)
                                        val percentage = if (totalInMonthData > 0) count.toFloat() / totalInMonthData else 0f
                                        NumberBox(
                                            number = number,
                                            count = count,
                                            percentage = percentage,
                                            modifier = Modifier.fillMaxWidth()
                                        )
                                    }
                                }
                            }
                            
                            // 只有当有绿波号码开出时才显示绿波分组
                            if (openedGreenNumbers.isNotEmpty()) {
                                Text(
                                    text = "绿波",
                                    fontWeight = FontWeight.Medium,
                                    fontSize = 14.sp,
                                    color = Color(0xFF81C784),
                                    modifier = Modifier.padding(vertical = 4.dp)
                                )
                                
                                // 只显示开出过的绿波号码
                                Column(verticalArrangement = Arrangement.spacedBy(8.dp)) {
                                    openedGreenNumbers.forEach { number ->
                                        val count = monthData.getOrDefault(number, 0)
                                        val percentage = if (totalInMonthData > 0) count.toFloat() / totalInMonthData else 0f
                                        NumberBox(
                                            number = number,
                                            count = count,
                                            percentage = percentage,
                                            modifier = Modifier.fillMaxWidth()
                                        )
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }
    
    @Composable
    fun NumberBox(
        number: Int,
        count: Int,
        percentage: Float,
        modifier: Modifier = Modifier
    ) {
        // 根据号码确定波色
        val waveColor = when {
            number in ZodiacUtils.RED_NUMBERS -> Color(0xFFE57373) // 红波
            number in ZodiacUtils.BLUE_NUMBERS -> Color(0xFF64B5F6) // 蓝波
            number in ZodiacUtils.GREEN_NUMBERS -> Color(0xFF81C784) // 绿波
            else -> Color.Gray // 默认颜色
        }
        
        Box(
            modifier = modifier
                .height(70.dp)
                .border(
                    width = 1.dp,
                    color = waveColor,
                    shape = RoundedCornerShape(8.dp)
                )
                .background(waveColor.copy(alpha = 0.1f), RoundedCornerShape(8.dp))
                .padding(8.dp)
        ) {
            Column(
                modifier = Modifier.fillMaxWidth(),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    // 号码和生肖
                    Column(
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        Text(
                            text = number.toString(),
                            fontSize = 16.sp,
                            fontWeight = FontWeight.Bold,
                            color = waveColor
                        )
                        
                        val zodiac = ZodiacUtils.getZodiacForNumber(number)
                        if (zodiac != null) {
                            Text(
                                text = zodiac,
                                fontSize = 12.sp,
                                color = waveColor
                            )
                        }
                    }
                    
                    // 次数和百分比
                    Column(
                        horizontalAlignment = Alignment.End
                    ) {
                        Text(
                            text = "$count 次",
                            fontSize = 14.sp,
                            color = MaterialTheme.colorScheme.onSurface
                        )
                        Text(
                            text = "(${String.format("%.1f", percentage * 100)}%)",
                            fontSize = 12.sp,
                            color = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                    }
                }
                
                Spacer(modifier = Modifier.height(8.dp))
                
                // 百分比指示器
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(8.dp)
                        .background(Color.LightGray.copy(alpha = 0.3f), RoundedCornerShape(4.dp))
                ) {
                    Box(
                        modifier = Modifier
                            .fillMaxWidth(percentage)
                            .height(8.dp)
                            .background(waveColor, RoundedCornerShape(4.dp))
                    )
                }
            }
        }
    }

    private suspend fun updateData(isFullUpdate: Boolean = true): String {
        return withContext(Dispatchers.IO) {
            try {
                // 检查存储权限 - 对于应用专用目录，不需要特殊权限
                // 创建保存目录
                if (!lotteryDataDir.exists()) {
                    val dirCreated = lotteryDataDir.mkdirs()
                    if (!dirCreated) {
                        println("调试: 无法创建历史数据目录: ${lotteryDataDir.absolutePath}")
                        return@withContext "错误：无法创建目录: ${lotteryDataDir.absolutePath}"
                    }
                }
                
                println("调试: 开始从网络获取数据... 模式: ${if (isFullUpdate) "全量更新" else "增量更新"}")
                
                // 确定需要获取的年份范围
                val startYear: Int
                val currentYear = Calendar.getInstance().get(Calendar.YEAR) // 获取当前年份
                
                if (isFullUpdate) {
                    // 全量更新：从2020年开始收集数据
                    startYear = 2020
                    println("调试: 全量更新，获取 $startYear 至 $currentYear 年的数据")
                } else {
                    // 增量更新：只获取当前年份的数据
                    startYear = currentYear
                    println("调试: 增量更新，只获取 $currentYear 年的数据")
                }
                
                val years = (startYear..currentYear).toList()
                var totalRecords = 0
                
                // 如果是增量更新，保留现有数据
                val allDataList = if (isFullUpdate) {
                    mutableListOf<ZodiacData>()
                } else {
                    historicalData.toMutableList()
                }
                
                val uniqueExpects = HashSet<String>() // 用于去重的集合
                // 如果是增量更新，先添加已有期号到去重集合
                if (!isFullUpdate) {
                    historicalData.forEach { uniqueExpects.add(it.expect) }
                    println("调试: 增量更新，已有 ${uniqueExpects.size} 条记录")
                }
                
                for (year in years) {
                    withContext(Dispatchers.Main) {
                        Toast.makeText(
                            this@PredictionActivity,
                            "正在获取 $year 年数据...",
                            Toast.LENGTH_SHORT
                        ).show()
                    }
                    
                    try {
                        val url = URL("https://history.macaumarksix.com/history/macaujc2/y/$year")
                        println("调试: 请求URL: $url")
                        
                        val connection = url.openConnection() as java.net.HttpURLConnection
                        connection.connectTimeout = 10000 // 10秒连接超时
                        connection.readTimeout = 15000 // 15秒读取超时
                        connection.requestMethod = "GET"
                        connection.setRequestProperty("User-Agent", "Mozilla/5.0")
                        
                        val responseCode = connection.responseCode
                        println("调试: HTTP响应码: $responseCode")
                        
                        if (responseCode == 200) {
                            val reader = BufferedReader(InputStreamReader(connection.inputStream))
                            val response = reader.readText()
                            reader.close()
                            
                            println("调试: 响应长度: ${response.length}")
                            
                            val jsonObject = JSONObject(response)
                            val dataArray = jsonObject.getJSONArray("data")
                            totalRecords += dataArray.length()
                            
                            println("调试: $year 年获取到 ${dataArray.length()} 条记录")
                            
                            withContext(Dispatchers.Main) {
                                Toast.makeText(
                                    this@PredictionActivity,
                                    "获取到 $year 年 ${dataArray.length()} 条记录",
                                    Toast.LENGTH_SHORT
                                ).show()
                            }
                            
                            // 处理每年数据
                            var newRecordsCount = 0
                            for (i in 0 until dataArray.length()) {
                                val item = dataArray.getJSONObject(i)
                                val expect = item.getString("expect")
                                
                                // 如果期号已存在，跳过
                                if (!uniqueExpects.add(expect)) {
                                    continue
                                }
                                
                                val openTime = item.getString("openTime")
                                val openCode = item.getString("openCode")
                                val wave = item.getString("wave")
                                val zodiac = item.getString("zodiac")
                                
                                val zodiacData = ZodiacData(
                                    expect = expect,
                                    zodiac = zodiac.split(","),
                                    openTime = openTime,
                                    openCode = openCode,
                                    wave = wave
                                )
                                allDataList.add(zodiacData)
                                newRecordsCount++
                                
                                // 打印前几条数据作为样本
                                if (i < 3) {
                                    println("调试: 样本数据 #$i: 期号=$expect, 开奖号码=$openCode")
                                }
                            }
                            
                            println("调试: $year 年新增 $newRecordsCount 条记录")
                            
                            // 如果是增量更新且已经获取到新数据，可以提前结束
                            if (!isFullUpdate && newRecordsCount > 0) {
                                println("调试: 增量更新已获取到新数据，提前结束")
                                break
                            }
                        } else {
                            println("调试: 获取 $year 年数据失败，HTTP响应码: $responseCode")
                        }
                    } catch (e: Exception) {
                        println("调试: 获取 $year 年数据出错: ${e.message}")
                        withContext(Dispatchers.Main) {
                            Toast.makeText(
                                this@PredictionActivity,
                                "$year 年数据获取失败: ${e.message}",
                                Toast.LENGTH_SHORT
                            ).show()
                        }
                        // 继续下一年的数据获取，不中断整个过程
                    }
                }
                
                println("调试: 网络数据获取完成，总共 $totalRecords 条记录，去重后 ${allDataList.size} 条")
                
                withContext(Dispatchers.Main) {
                    Toast.makeText(
                        this@PredictionActivity,
                        "总共获取到 $totalRecords 条记录，去重后 ${allDataList.size} 条，正在保存...",
                        Toast.LENGTH_SHORT
                    ).show()
                }
                
                // 按期号排序（通常期号格式为年份+序号，所以按期号排序会按时间顺序）
                allDataList.sortByDescending { it.expect }
                
                // 更新内存中的数据
                historicalData.clear()
                historicalData.addAll(allDataList)
                
                // 保存到CSV文件，使用固定的文件名
                val csvFile = File(lotteryDataDir, "澳门开奖历史.csv")
                println("调试: 保存CSV文件到: ${csvFile.absolutePath}")
                
                try {
                    val csvWriter = csvFile.bufferedWriter()
                    
                    // 写入CSV表头
                    csvWriter.write("期数,开奖时间,开奖号码,波段,生肖\n")
                    
                    // 写入所有记录
                    for (data in allDataList) {
                        csvWriter.write("${data.expect},${data.openTime},${data.openCode},${data.wave},${data.zodiac.joinToString(",")}\n")
                    }
                    
                    // 关闭CSV写入器
                    csvWriter.close()
                    
                    println("调试: CSV文件保存成功，共 ${allDataList.size} 条记录")
                    
                    withContext(Dispatchers.Main) {
                        Toast.makeText(
                            this@PredictionActivity,
                            "数据已保存到: ${csvFile.name}",
                            Toast.LENGTH_LONG
                        ).show()
                    }
                    
                    return@withContext csvFile.absolutePath
                } catch (e: Exception) {
                    e.printStackTrace()
                    println("调试: 保存文件失败: ${e.message}")
                    return@withContext "错误：保存文件失败: ${e.message}"
                }
            } catch (e: Exception) {
                e.printStackTrace()
                println("调试: 更新数据过程中发生异常: ${e.message}")
                return@withContext "错误：更新数据失败: ${e.message}"
            }
        }
    }
    
    // 加载最新的数据文件
    private suspend fun loadLatestData(): String {
        return withContext(Dispatchers.IO) {
            try {
                println("调试: 开始加载历史数据，文件路径: ${historyFile.absolutePath}")
                
                // 检查文件是否存在
                if (!historyFile.exists()) {
                    println("调试: 历史数据文件不存在")
                    return@withContext "错误：历史数据文件不存在，请先更新数据"
                }
                
                if (!historyFile.canRead()) {
                    println("调试: 历史数据文件无法读取")
                    return@withContext "错误：历史数据文件无法读取"
                }
                
                println("调试: 历史数据文件存在且可读，大小: ${historyFile.length()} 字节")
                
                try {
                    // 清空现有数据
                    historicalData.clear()
                    
                    // 读取CSV文件
                    val reader = historyFile.bufferedReader()
                    var line = reader.readLine() // 读取表头
                    
                    if (line == null) {
                        reader.close()
                        println("调试: CSV文件为空")
                        return@withContext "错误：历史数据文件为空"
                    }
                    
                    println("调试: CSV表头: $line")
                    
                    var count = 0
                    while (reader.readLine()?.also { line = it } != null) {
                        try {
                            val parts = line.split(",")
                            if (parts.size >= 5) {
                                val expect = parts[0].trim()
                                val openTime = parts[1].trim()
                                val openCode = parts[2].trim()
                                val wave = parts[3].trim()
                                val zodiacStr = parts[4].trim()
                                
                                val zodiacData = ZodiacData(
                                    expect = expect,
                                    openTime = openTime,
                                    openCode = openCode,
                                    wave = wave,
                                    zodiac = zodiacStr.split(",").map { it.trim() }
                                )
                                
                                historicalData.add(zodiacData)
                                count++
                                
                                // 每100条数据打印一次样本
                                if (count % 100 == 0 || count < 5) {
                                    println("调试: 样本数据 #$count: 期号=$expect, 开奖号码=$openCode")
                                }
                            }
                        } catch (e: Exception) {
                            println("调试: 解析行失败: $line, 错误: ${e.message}")
                            // 继续处理下一行
                        }
                    }
                    
                    reader.close()
                    
                    if (count == 0) {
                        println("调试: 未读取到有效记录")
                        return@withContext "错误：未读取到有效记录"
                    }
                    
                    // 按期号排序
                    historicalData.sortByDescending { it.expect }
                    
                    println("调试: 成功加载了 $count 条历史记录")
                    return@withContext "成功加载了 $count 条历史记录"
                } catch (e: Exception) {
                    e.printStackTrace()
                    println("调试: 读取文件失败: ${e.message}")
                    return@withContext "错误：读取文件失败: ${e.message}"
                }
            } catch (e: Exception) {
                e.printStackTrace()
                println("调试: 加载数据过程中发生异常: ${e.message}")
                return@withContext "错误：加载数据失败: ${e.message}"
            }
        }
    }
    
    // 添加新开奖数据
    private fun addNewLotteryData(expect: String, openTime: String, openCode: String) {
        // 验证开奖号码格式
        val isValidFormat = verifyOpenCodeFormat(openCode)
        if (!isValidFormat) {
            Toast.makeText(this, "开奖号码格式错误，请确保包含7个号码(前区6个+特码)", Toast.LENGTH_SHORT).show()
            return
        }
        
        // 解析开奖号码
        val codeArray = openCode.split("+")
        if (codeArray.size != 2) {
            Toast.makeText(this, "开奖号码格式错误，请使用'+'分隔前后区号码", Toast.LENGTH_SHORT).show()
            return
        }
        
        // 计算波段
        val specialNumber = codeArray[1].toIntOrNull()
        if (specialNumber == null) {
            Toast.makeText(this, "特码格式错误，无法解析为数字", Toast.LENGTH_SHORT).show()
            return
        }
        
        val wave = when {
            specialNumber in 1..10 -> "红波"
            specialNumber in 11..20 -> "蓝波"
            else -> "绿波"
        }
        
        // 创建新数据
        val zodiacList = listOf("鼠", "牛", "虎", "兔", "龙", "蛇", "马", "羊", "猴", "鸡", "狗", "猪")
        // 根据特殊号码简单计算生肖（这里只是示例，实际可能需要更复杂的计算）
        val zodiacIndex = (specialNumber - 1) % 12
        val zodiac = listOf(zodiacList[zodiacIndex])
        
        val newData = ZodiacData(
            expect = expect,
            openTime = openTime,
            openCode = openCode,
            wave = wave,
            zodiac = zodiac
        )
        
        // 添加到历史数据中
        val found = historicalData.any { it.expect == expect }
        if (found) {
            // 替换已有的数据
            historicalData.removeAll { it.expect == expect }
            historicalData.add(newData)
            Toast.makeText(this, "已更新期号 $expect 的数据", Toast.LENGTH_SHORT).show()
        } else {
            // 添加新数据
            historicalData.add(newData)
            // 按期数排序
            historicalData.sortByDescending { it.expect }
            Toast.makeText(this, "已添加期号 $expect 的数据", Toast.LENGTH_SHORT).show()
        }
        
        // 保存到文件
        CoroutineScope(Dispatchers.IO).launch {
            try {
                saveDataToCsv()
                withContext(Dispatchers.Main) {
                    Toast.makeText(this@PredictionActivity, "数据已保存", Toast.LENGTH_SHORT).show()
                }
            } catch (e: Exception) {
                withContext(Dispatchers.Main) {
                    Toast.makeText(this@PredictionActivity, "保存失败: ${e.message}", Toast.LENGTH_SHORT).show()
                }
            }
        }
    }
    
    // 验证开奖号码格式是否正确
    private fun verifyOpenCodeFormat(openCode: String): Boolean {
        try {
            if (openCode.contains("+")) {
                // 标准格式 01,02,03,04,05,06+07
                val parts = openCode.split("+")
                if (parts.size != 2) return false
                
                val frontPart = parts[0]
                val backPart = parts[1]
                
                // 前区应该有6个用逗号分隔的号码
                val frontNumbers = frontPart.split(",")
                if (frontNumbers.size != 6) return false
                
                // 检查所有号码是否为数字
                for (num in frontNumbers) {
                    num.trim().toInt() // 尝试转换为数字，失败会抛出异常
                }
                
                // 检查特别号码
                backPart.trim().toInt()
                
                return true
            } else if (openCode.contains(",")) {
                // 逗号分隔格式 01,02,03,04,05,06,07
                val numbers = openCode.split(",")
                
                // 应该有7个号码
                if (numbers.size != 7) return false
                
                // 检查所有号码是否为数字
                for (num in numbers) {
                    num.trim().toInt()
                }
                
                return true
            } else if (openCode.contains(" ")) {
                // 空格分隔格式 01 02 03 04 05 06 07
                val numbers = openCode.split(" ").filter { it.isNotBlank() }
                
                // 应该有7个号码
                if (numbers.size != 7) return false
                
                // 检查所有号码是否为数字
                for (num in numbers) {
                    num.trim().toInt()
                }
                
                return true
            }
            
            return false
        } catch (e: Exception) {
            return false
        }
    }
    
    // 保存数据到CSV文件
    private suspend fun saveDataToCsv() {
        withContext(Dispatchers.IO) {
            // 检查目录是否存在
            if (!lotteryDataDir.exists()) {
                val dirCreated = lotteryDataDir.mkdirs()
                if (!dirCreated) {
                    println("调试: 无法创建历史数据目录: ${lotteryDataDir.absolutePath}")
                    throw IOException("无法创建目录: ${lotteryDataDir.absolutePath}")
                }
            }
            
            // 保存到CSV文件
            val csvFile = File(lotteryDataDir, "澳门开奖历史.csv")
            println("调试: 保存CSV文件到: ${csvFile.absolutePath}")
            
            val csvWriter = csvFile.bufferedWriter()
            
            // 写入CSV表头
            csvWriter.write("期数,开奖时间,开奖号码,波段,生肖\n")
            
            // 写入所有记录
            for (data in historicalData) {
                csvWriter.write("${data.expect},${data.openTime},${data.openCode},${data.wave},${data.zodiac.joinToString(",")}\n")
            }
            
            // 关闭CSV写入器
            csvWriter.close()
            
            println("调试: CSV文件保存成功，共 ${historicalData.size} 条记录")
        }
    }
}
