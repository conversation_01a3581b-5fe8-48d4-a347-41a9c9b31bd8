package com.example.myxuanmazhushou

import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.Refresh
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.example.myxuanmazhushou.prediction.AutoPredictionManager
import com.example.myxuanmazhushou.prediction.AutoPredictionResult
import com.example.myxuanmazhushou.prediction.AutoPredictionStage
import com.example.myxuanmazhushou.prediction.PredictionResult
import com.example.myxuanmazhushou.ui.theme.MyXuanmazhushouTheme
import kotlinx.coroutines.launch

/**
 * 智能预测界面
 * 集成自动数据检查、下载、预测计算的完整流程
 */
class SmartPredictionActivity : ComponentActivity() {
    
    private lateinit var autoPredictionManager: AutoPredictionManager
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        autoPredictionManager = AutoPredictionManager(this)
        
        setContent {
            MyXuanmazhushouTheme {
                SmartPredictionScreen(
                    onBackClick = { finish() },
                    autoPredictionManager = autoPredictionManager
                )
            }
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun SmartPredictionScreen(
    onBackClick: () -> Unit,
    autoPredictionManager: AutoPredictionManager
) {
    var isLoading by remember { mutableStateOf(false) }
    var currentStage by remember { mutableStateOf<AutoPredictionStage?>(null) }
    var progress by remember { mutableStateOf(0) }
    var statusMessage by remember { mutableStateOf("") }
    var predictionResult by remember { mutableStateOf<AutoPredictionResult?>(null) }
    
    val scope = rememberCoroutineScope()
    
    // 自动执行预测流程
    LaunchedEffect(Unit) {
        scope.launch {
            isLoading = true
            
            val result = autoPredictionManager.performAutoPrediction { stage, prog, message ->
                currentStage = stage
                progress = prog
                statusMessage = message
            }
            
            predictionResult = result
            isLoading = false
        }
    }
    
    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text("智能预测") },
                navigationIcon = {
                    IconButton(onClick = onBackClick) {
                        Icon(
                            imageVector = Icons.Default.ArrowBack,
                            contentDescription = "返回"
                        )
                    }
                },
                actions = {
                    IconButton(
                        onClick = {
                            scope.launch {
                                isLoading = true
                                predictionResult = null
                                
                                val result = autoPredictionManager.performAutoPrediction { stage, prog, message ->
                                    currentStage = stage
                                    progress = prog
                                    statusMessage = message
                                }
                                
                                predictionResult = result
                                isLoading = false
                            }
                        },
                        enabled = !isLoading
                    ) {
                        Icon(
                            imageVector = Icons.Default.Refresh,
                            contentDescription = "刷新预测"
                        )
                    }
                }
            )
        }
    ) { innerPadding ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(innerPadding)
                .padding(16.dp)
        ) {
            // 状态显示区域
            StatusCard(
                isLoading = isLoading,
                currentStage = currentStage,
                progress = progress,
                statusMessage = statusMessage,
                dataStatus = predictionResult?.dataStatus
            )
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // 预测结果显示区域
            if (predictionResult != null && predictionResult!!.success) {
                PredictionResultCard(predictionResult!!.predictionResult)
            } else if (predictionResult != null && !predictionResult!!.success) {
                ErrorCard(predictionResult!!.message)
            }
        }
    }
}

@Composable
fun StatusCard(
    isLoading: Boolean,
    currentStage: AutoPredictionStage?,
    progress: Int,
    statusMessage: String,
    dataStatus: com.example.myxuanmazhushou.data.DataStatus?
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "系统状态",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold
            )
            
            Spacer(modifier = Modifier.height(8.dp))
            
            if (isLoading) {
                // 显示进度
                LinearProgressIndicator(
                    progress = progress / 100f,
                    modifier = Modifier.fillMaxWidth()
                )
                
                Spacer(modifier = Modifier.height(8.dp))
                
                Text(
                    text = getStageDescription(currentStage),
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.primary
                )
                
                Text(
                    text = statusMessage,
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
                )
            } else {
                // 显示数据状态
                dataStatus?.let { status ->
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.SpaceBetween
                    ) {
                        Text(
                            text = "数据状态:",
                            style = MaterialTheme.typography.bodyMedium
                        )
                        Text(
                            text = if (status.hasData) "已加载" else "无数据",
                            style = MaterialTheme.typography.bodyMedium,
                            color = if (status.hasData) Color.Green else Color.Red
                        )
                    }
                    
                    if (status.hasData) {
                        Row(
                            modifier = Modifier.fillMaxWidth(),
                            horizontalArrangement = Arrangement.SpaceBetween
                        ) {
                            Text(
                                text = "数据量:",
                                style = MaterialTheme.typography.bodySmall
                            )
                            Text(
                                text = "${status.dataCount} 条",
                                style = MaterialTheme.typography.bodySmall
                            )
                        }
                        
                        Row(
                            modifier = Modifier.fillMaxWidth(),
                            horizontalArrangement = Arrangement.SpaceBetween
                        ) {
                            Text(
                                text = "最新期号:",
                                style = MaterialTheme.typography.bodySmall
                            )
                            Text(
                                text = status.latestPeriod ?: "未知",
                                style = MaterialTheme.typography.bodySmall
                            )
                        }
                        
                        Row(
                            modifier = Modifier.fillMaxWidth(),
                            horizontalArrangement = Arrangement.SpaceBetween
                        ) {
                            Text(
                                text = "最后更新:",
                                style = MaterialTheme.typography.bodySmall
                            )
                            Text(
                                text = status.lastUpdateTimeFormatted,
                                style = MaterialTheme.typography.bodySmall
                            )
                        }
                    }
                }
            }
        }
    }
}

@Composable
fun PredictionResultCard(predictionResult: PredictionResult) {
    LazyColumn(
        verticalArrangement = Arrangement.spacedBy(12.dp)
    ) {
        // 生肖预测
        item {
            Card(
                modifier = Modifier.fillMaxWidth(),
                elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
            ) {
                Column(
                    modifier = Modifier.padding(16.dp)
                ) {
                    Text(
                        text = "生肖预测",
                        style = MaterialTheme.typography.titleMedium,
                        fontWeight = FontWeight.Bold,
                        color = MaterialTheme.colorScheme.primary
                    )
                    
                    Spacer(modifier = Modifier.height(8.dp))
                    
                    predictionResult.zodiacPrediction.recommendations.take(3).forEach { recommendation ->
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(vertical = 4.dp),
                            horizontalArrangement = Arrangement.SpaceBetween,
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Text(
                                text = recommendation.zodiac,
                                style = MaterialTheme.typography.bodyLarge,
                                fontWeight = FontWeight.Medium
                            )
                            
                            Column(
                                horizontalAlignment = Alignment.End
                            ) {
                                Text(
                                    text = "${String.format("%.1f", recommendation.confidence * 100)}%",
                                    style = MaterialTheme.typography.bodyMedium,
                                    color = MaterialTheme.colorScheme.primary
                                )
                                Text(
                                    text = recommendation.reason,
                                    style = MaterialTheme.typography.bodySmall,
                                    color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f)
                                )
                            }
                        }
                    }
                }
            }
        }
        
        // 号码预测
        item {
            Card(
                modifier = Modifier.fillMaxWidth(),
                elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
            ) {
                Column(
                    modifier = Modifier.padding(16.dp)
                ) {
                    Text(
                        text = "号码预测",
                        style = MaterialTheme.typography.titleMedium,
                        fontWeight = FontWeight.Bold,
                        color = MaterialTheme.colorScheme.primary
                    )
                    
                    Spacer(modifier = Modifier.height(8.dp))
                    
                    // 显示推荐号码网格
                    val recommendations = predictionResult.numberPrediction.recommendations.take(6)
                    val chunkedRecommendations = recommendations.chunked(3)
                    
                    chunkedRecommendations.forEach { rowRecommendations ->
                        Row(
                            modifier = Modifier.fillMaxWidth(),
                            horizontalArrangement = Arrangement.SpaceEvenly
                        ) {
                            rowRecommendations.forEach { recommendation ->
                                NumberChip(
                                    number = recommendation.number.toString(),
                                    confidence = recommendation.confidence,
                                    modifier = Modifier.weight(1f)
                                )
                            }
                            // 填充空白
                            repeat(3 - rowRecommendations.size) {
                                Spacer(modifier = Modifier.weight(1f))
                            }
                        }
                        Spacer(modifier = Modifier.height(8.dp))
                    }
                }
            }
        }

        // 波色预测
        item {
            Card(
                modifier = Modifier.fillMaxWidth(),
                elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
            ) {
                Column(
                    modifier = Modifier.padding(16.dp)
                ) {
                    Text(
                        text = "波色预测",
                        style = MaterialTheme.typography.titleMedium,
                        fontWeight = FontWeight.Bold,
                        color = MaterialTheme.colorScheme.primary
                    )

                    Spacer(modifier = Modifier.height(8.dp))

                    predictionResult.colorPrediction.recommendations.forEach { recommendation ->
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(vertical = 4.dp),
                            horizontalArrangement = Arrangement.SpaceBetween,
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Row(
                                verticalAlignment = Alignment.CenterVertically
                            ) {
                                Box(
                                    modifier = Modifier
                                        .size(16.dp)
                                        .background(
                                            color = when (recommendation.color) {
                                                "红" -> Color.Red
                                                "绿" -> Color.Green
                                                else -> Color.Blue
                                            },
                                            shape = RoundedCornerShape(8.dp)
                                        )
                                )
                                Spacer(modifier = Modifier.width(8.dp))
                                Text(
                                    text = "${recommendation.color}波",
                                    style = MaterialTheme.typography.bodyLarge,
                                    fontWeight = FontWeight.Medium
                                )
                            }

                            Text(
                                text = "${String.format("%.1f", recommendation.confidence * 100)}%",
                                style = MaterialTheme.typography.bodyMedium,
                                color = MaterialTheme.colorScheme.primary
                            )
                        }
                    }
                }
            }
        }

        // 趋势分析
        item {
            Card(
                modifier = Modifier.fillMaxWidth(),
                elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
            ) {
                Column(
                    modifier = Modifier.padding(16.dp)
                ) {
                    Text(
                        text = "趋势分析",
                        style = MaterialTheme.typography.titleMedium,
                        fontWeight = FontWeight.Bold,
                        color = MaterialTheme.colorScheme.primary
                    )

                    Spacer(modifier = Modifier.height(8.dp))

                    if (predictionResult.trendAnalysis.recentTrends.isNotEmpty()) {
                        predictionResult.trendAnalysis.recentTrends.forEach { trend ->
                            Text(
                                text = "• $trend",
                                style = MaterialTheme.typography.bodyMedium,
                                modifier = Modifier.padding(vertical = 2.dp)
                            )
                        }
                    } else {
                        Text(
                            text = "暂无明显趋势",
                            style = MaterialTheme.typography.bodyMedium,
                            color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f)
                        )
                    }

                    Spacer(modifier = Modifier.height(8.dp))

                    Text(
                        text = "基于最近 ${predictionResult.trendAnalysis.analyzedPeriods} 期数据分析",
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.5f)
                    )
                }
            }
        }
    }
}

@Composable
fun ErrorCard(message: String) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.errorContainer
        )
    ) {
        Column(
            modifier = Modifier.padding(16.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Text(
                text = "预测失败",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold,
                color = MaterialTheme.colorScheme.onErrorContainer
            )

            Spacer(modifier = Modifier.height(8.dp))

            Text(
                text = message,
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.onErrorContainer,
                textAlign = TextAlign.Center
            )
        }
    }
}

@Composable
fun NumberChip(
    number: String,
    confidence: Double,
    modifier: Modifier = Modifier
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        modifier = modifier.padding(4.dp)
    ) {
        Surface(
            shape = RoundedCornerShape(8.dp),
            color = MaterialTheme.colorScheme.primary.copy(alpha = 0.1f),
            border = androidx.compose.foundation.BorderStroke(
                width = 1.dp,
                color = MaterialTheme.colorScheme.primary.copy(alpha = 0.3f)
            ),
            modifier = Modifier.size(40.dp)
        ) {
            Box(
                contentAlignment = Alignment.Center,
                modifier = Modifier.fillMaxSize()
            ) {
                Text(
                    text = number,
                    style = MaterialTheme.typography.bodyLarge,
                    fontWeight = FontWeight.Medium,
                    color = MaterialTheme.colorScheme.primary
                )
            }
        }

        Spacer(modifier = Modifier.height(4.dp))

        Text(
            text = "${String.format("%.0f", confidence * 100)}%",
            style = MaterialTheme.typography.bodySmall,
            color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f)
        )
    }
}

fun getStageDescription(stage: AutoPredictionStage?): String {
    return when (stage) {
        AutoPredictionStage.CHECKING_DATA -> "检查数据状态"
        AutoPredictionStage.LOADING_DATA -> "加载本地数据"
        AutoPredictionStage.CHECKING_UPDATES -> "检查数据更新"
        AutoPredictionStage.DOWNLOADING_DATA -> "下载数据"
        AutoPredictionStage.CALCULATING_PREDICTION -> "计算预测"
        AutoPredictionStage.COMPLETED -> "预测完成"
        null -> "准备中"
    }
}
