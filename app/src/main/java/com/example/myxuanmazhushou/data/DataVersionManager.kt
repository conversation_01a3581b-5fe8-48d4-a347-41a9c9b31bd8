package com.example.myxuanmazhushou.data

import android.content.Context
import android.content.SharedPreferences
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import org.json.JSONObject
import java.net.URL
import java.text.SimpleDateFormat
import java.util.*

/**
 * 数据版本管理器
 * 负责管理本地数据版本、检查数据是否为最新、记录最后更新时间等
 */
class DataVersionManager(private val context: Context) {
    
    companion object {
        private const val PREFS_NAME = "data_version_prefs"
        private const val KEY_LAST_UPDATE_TIME = "last_update_time"
        private const val KEY_LATEST_PERIOD = "latest_period"
        private const val KEY_DATA_COUNT = "data_count"
        private const val KEY_LAST_CHECK_TIME = "last_check_time"
        
        // 检查间隔（毫秒）- 5分钟
        private const val CHECK_INTERVAL = 5 * 60 * 1000L
    }
    
    private val prefs: SharedPreferences = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
    
    /**
     * 获取最后更新时间
     */
    fun getLastUpdateTime(): Long {
        return prefs.getLong(KEY_LAST_UPDATE_TIME, 0L)
    }
    
    /**
     * 设置最后更新时间
     */
    fun setLastUpdateTime(time: Long = System.currentTimeMillis()) {
        prefs.edit().putLong(KEY_LAST_UPDATE_TIME, time).apply()
    }
    
    /**
     * 获取本地最新期号
     */
    fun getLatestPeriod(): String? {
        return prefs.getString(KEY_LATEST_PERIOD, null)
    }
    
    /**
     * 设置本地最新期号
     */
    fun setLatestPeriod(period: String) {
        prefs.edit().putString(KEY_LATEST_PERIOD, period).apply()
    }
    
    /**
     * 获取本地数据条数
     */
    fun getDataCount(): Int {
        return prefs.getInt(KEY_DATA_COUNT, 0)
    }
    
    /**
     * 设置本地数据条数
     */
    fun setDataCount(count: Int) {
        prefs.edit().putInt(KEY_DATA_COUNT, count).apply()
    }
    
    /**
     * 获取最后检查时间
     */
    private fun getLastCheckTime(): Long {
        return prefs.getLong(KEY_LAST_CHECK_TIME, 0L)
    }
    
    /**
     * 设置最后检查时间
     */
    private fun setLastCheckTime(time: Long = System.currentTimeMillis()) {
        prefs.edit().putLong(KEY_LAST_CHECK_TIME, time).apply()
    }
    
    /**
     * 检查是否需要更新数据
     * @return Pair<Boolean, String> - 第一个值表示是否需要更新，第二个值是描述信息
     */
    suspend fun checkForUpdates(): Pair<Boolean, String> {
        return withContext(Dispatchers.IO) {
            try {
                val currentTime = System.currentTimeMillis()
                val lastCheckTime = getLastCheckTime()
                
                // 如果距离上次检查时间不足5分钟，直接返回不需要更新
                if (currentTime - lastCheckTime < CHECK_INTERVAL) {
                    val remainingTime = (CHECK_INTERVAL - (currentTime - lastCheckTime)) / 1000
                    return@withContext Pair(false, "距离上次检查不足5分钟，剩余${remainingTime}秒")
                }
                
                // 更新检查时间
                setLastCheckTime(currentTime)
                
                val localLatestPeriod = getLatestPeriod()
                val localDataCount = getDataCount()
                
                // 如果本地没有数据，需要全量下载
                if (localLatestPeriod == null || localDataCount == 0) {
                    return@withContext Pair(true, "本地无数据，需要全量下载")
                }
                
                // 检查网络最新数据
                val remoteInfo = getRemoteLatestInfo()
                if (remoteInfo == null) {
                    return@withContext Pair(false, "无法获取网络数据信息")
                }
                
                val remoteLatestPeriod = remoteInfo.first
                val remoteDataCount = remoteInfo.second
                
                // 比较期号和数据量
                if (remoteLatestPeriod != localLatestPeriod || remoteDataCount > localDataCount) {
                    return@withContext Pair(true, "发现新数据：远程最新期号 $remoteLatestPeriod，本地最新期号 $localLatestPeriod")
                }
                
                return@withContext Pair(false, "数据已是最新")
                
            } catch (e: Exception) {
                return@withContext Pair(false, "检查更新失败：${e.message}")
            }
        }
    }
    
    /**
     * 获取远程最新数据信息
     * @return Pair<String, Int>? - 最新期号和数据总数，失败时返回null
     */
    private suspend fun getRemoteLatestInfo(): Pair<String, Int>? {
        return try {
            val currentYear = Calendar.getInstance().get(Calendar.YEAR)
            val url = URL("https://history.macaumarksix.com/history/macaujc2/y/$currentYear")
            
            val connection = url.openConnection() as java.net.HttpURLConnection
            connection.connectTimeout = 10000
            connection.readTimeout = 15000
            connection.requestMethod = "GET"
            connection.setRequestProperty("User-Agent", "Mozilla/5.0")
            
            if (connection.responseCode == 200) {
                val response = connection.inputStream.bufferedReader().readText()
                val jsonObject = JSONObject(response)
                val dataArray = jsonObject.getJSONArray("data")
                
                if (dataArray.length() > 0) {
                    // 获取最新一期数据
                    val latestItem = dataArray.getJSONObject(0)
                    val latestPeriod = latestItem.getString("expect")
                    val totalCount = dataArray.length()
                    
                    Pair(latestPeriod, totalCount)
                } else {
                    null
                }
            } else {
                null
            }
        } catch (e: Exception) {
            null
        }
    }
    
    /**
     * 获取数据状态信息
     */
    fun getDataStatus(): DataStatus {
        val lastUpdateTime = getLastUpdateTime()
        val latestPeriod = getLatestPeriod()
        val dataCount = getDataCount()
        
        return DataStatus(
            hasData = dataCount > 0,
            lastUpdateTime = lastUpdateTime,
            latestPeriod = latestPeriod,
            dataCount = dataCount,
            lastUpdateTimeFormatted = if (lastUpdateTime > 0) {
                SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.CHINA).format(Date(lastUpdateTime))
            } else {
                "从未更新"
            }
        )
    }
    
    /**
     * 更新数据状态
     */
    fun updateDataStatus(latestPeriod: String, dataCount: Int) {
        setLatestPeriod(latestPeriod)
        setDataCount(dataCount)
        setLastUpdateTime()
    }
}

/**
 * 数据状态信息
 */
data class DataStatus(
    val hasData: Boolean,
    val lastUpdateTime: Long,
    val latestPeriod: String?,
    val dataCount: Int,
    val lastUpdateTimeFormatted: String
)
