package com.example.myxuanmazhushou.data

import android.content.Context
import android.widget.Toast
import com.example.myxuanmazhushou.utils.ZodiacData
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import org.json.JSONObject
import java.io.BufferedReader
import java.io.File
import java.io.InputStreamReader
import java.net.URL
import java.util.*

/**
 * 智能数据下载管理器
 * 负责智能增量下载，只获取新的数据而不是全量下载
 */
class SmartDataDownloader(
    private val context: Context,
    private val dataVersionManager: DataVersionManager
) {
    
    companion object {
        private const val BASE_URL = "https://history.macaumarksix.com/history/macaujc2/y/"
    }
    
    /**
     * 智能下载数据
     * @param onProgress 进度回调 (当前进度, 总进度, 描述)
     * @return 下载结果和新增数据列表
     */
    suspend fun smartDownload(
        onProgress: (Int, Int, String) -> Unit = { _, _, _ -> }
    ): DownloadResult {
        return withContext(Dispatchers.IO) {
            try {
                val dataStatus = dataVersionManager.getDataStatus()
                
                if (!dataStatus.hasData) {
                    // 本地无数据，执行全量下载
                    onProgress(0, 100, "本地无数据，开始全量下载...")
                    fullDownload(onProgress)
                } else {
                    // 本地有数据，执行增量下载
                    onProgress(0, 100, "检查增量更新...")
                    incrementalDownload(onProgress)
                }
            } catch (e: Exception) {
                DownloadResult(
                    success = false,
                    message = "下载失败：${e.message}",
                    newDataList = emptyList(),
                    totalCount = 0
                )
            }
        }
    }
    
    /**
     * 全量下载
     */
    private suspend fun fullDownload(
        onProgress: (Int, Int, String) -> Unit
    ): DownloadResult {
        val currentYear = Calendar.getInstance().get(Calendar.YEAR)
        val startYear = 2020
        val years = (startYear..currentYear).toList()
        val allDataList = mutableListOf<ZodiacData>()
        val uniqueExpects = HashSet<String>()
        
        onProgress(0, years.size, "开始全量下载 ${years.size} 年数据...")
        
        for ((index, year) in years.withIndex()) {
            onProgress(index, years.size, "正在下载 $year 年数据...")
            
            try {
                val yearData = downloadYearData(year)
                var newRecordsCount = 0
                
                yearData.forEach { data ->
                    if (uniqueExpects.add(data.expect)) {
                        allDataList.add(data)
                        newRecordsCount++
                    }
                }
                
                onProgress(index + 1, years.size, "$year 年完成，新增 $newRecordsCount 条记录")
                
            } catch (e: Exception) {
                onProgress(index + 1, years.size, "$year 年下载失败：${e.message}")
            }
        }
        
        if (allDataList.isNotEmpty()) {
            // 按期号排序（最新的在前面）
            allDataList.sortByDescending { it.expect }
            
            // 更新版本信息
            dataVersionManager.updateDataStatus(
                allDataList.first().expect,
                allDataList.size
            )
        }
        
        return DownloadResult(
            success = allDataList.isNotEmpty(),
            message = if (allDataList.isNotEmpty()) "全量下载完成，共获取 ${allDataList.size} 条记录" else "全量下载失败",
            newDataList = allDataList,
            totalCount = allDataList.size
        )
    }
    
    /**
     * 增量下载
     */
    private suspend fun incrementalDownload(
        onProgress: (Int, Int, String) -> Unit
    ): DownloadResult {
        val currentYear = Calendar.getInstance().get(Calendar.YEAR)
        val localLatestPeriod = dataVersionManager.getLatestPeriod()
        
        onProgress(0, 100, "检查 $currentYear 年最新数据...")
        
        try {
            val currentYearData = downloadYearData(currentYear)
            val newDataList = mutableListOf<ZodiacData>()
            
            // 找到本地最新期号的位置
            val localLatestIndex = if (localLatestPeriod != null) {
                currentYearData.indexOfFirst { it.expect == localLatestPeriod }
            } else {
                -1
            }
            
            if (localLatestIndex == -1) {
                // 本地最新期号不在当前年份数据中，可能需要下载所有当前年份数据
                onProgress(50, 100, "本地数据较旧，下载当前年份所有数据...")
                newDataList.addAll(currentYearData)
            } else {
                // 只下载比本地最新期号更新的数据
                val newerData = currentYearData.take(localLatestIndex)
                newDataList.addAll(newerData)
                onProgress(50, 100, "发现 ${newerData.size} 条新数据")
            }
            
            if (newDataList.isNotEmpty()) {
                // 按期号排序（最新的在前面）
                newDataList.sortByDescending { it.expect }
                
                // 更新版本信息
                val newTotalCount = dataVersionManager.getDataCount() + newDataList.size
                dataVersionManager.updateDataStatus(
                    newDataList.first().expect,
                    newTotalCount
                )
                
                onProgress(100, 100, "增量下载完成，新增 ${newDataList.size} 条记录")
            } else {
                onProgress(100, 100, "数据已是最新，无需更新")
            }
            
            return DownloadResult(
                success = true,
                message = if (newDataList.isNotEmpty()) "增量下载完成，新增 ${newDataList.size} 条记录" else "数据已是最新",
                newDataList = newDataList,
                totalCount = dataVersionManager.getDataCount()
            )
            
        } catch (e: Exception) {
            return DownloadResult(
                success = false,
                message = "增量下载失败：${e.message}",
                newDataList = emptyList(),
                totalCount = 0
            )
        }
    }
    
    /**
     * 下载指定年份的数据
     */
    private suspend fun downloadYearData(year: Int): List<ZodiacData> {
        val url = URL("$BASE_URL$year")
        val connection = url.openConnection() as java.net.HttpURLConnection
        
        connection.connectTimeout = 10000
        connection.readTimeout = 15000
        connection.requestMethod = "GET"
        connection.setRequestProperty("User-Agent", "Mozilla/5.0")
        
        if (connection.responseCode != 200) {
            throw Exception("HTTP ${connection.responseCode}")
        }
        
        val reader = BufferedReader(InputStreamReader(connection.inputStream))
        val response = reader.readText()
        reader.close()
        
        val jsonObject = JSONObject(response)
        val dataArray = jsonObject.getJSONArray("data")
        val dataList = mutableListOf<ZodiacData>()
        
        for (i in 0 until dataArray.length()) {
            val item = dataArray.getJSONObject(i)
            val expect = item.getString("expect")
            val openTime = item.getString("openTime")
            val openCode = item.getString("openCode")
            val wave = item.getString("wave")
            val zodiac = item.getString("zodiac")
            
            val zodiacData = ZodiacData(
                expect = expect,
                zodiac = zodiac.split(","),
                openTime = openTime,
                openCode = openCode,
                wave = wave
            )
            dataList.add(zodiacData)
        }
        
        return dataList
    }
}

/**
 * 下载结果
 */
data class DownloadResult(
    val success: Boolean,
    val message: String,
    val newDataList: List<ZodiacData>,
    val totalCount: Int
)
