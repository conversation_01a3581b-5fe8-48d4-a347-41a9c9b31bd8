package com.example.myxuanmazhushou.prediction

import android.content.Context
import com.example.myxuanmazhushou.data.DataVersionManager
import com.example.myxuanmazhushou.data.SmartDataDownloader
import com.example.myxuanmazhushou.utils.ZodiacData
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.io.BufferedReader
import java.io.File
import java.io.FileReader

/**
 * 自动预测管理器
 * 将数据检查、下载、计算整合为自动化流程
 */
class AutoPredictionManager(private val context: Context) {
    
    private val dataVersionManager = DataVersionManager(context)
    private val smartDownloader = SmartDataDownloader(context, dataVersionManager)
    private val predictionEngine = PredictionEngine()
    
    // 数据存储目录
    private val lotteryDataDir by lazy {
        val dataDir = File(context.filesDir, "lottery_data")
        if (!dataDir.exists()) {
            dataDir.mkdirs()
        }
        dataDir
    }
    
    // 历史数据文件
    private val historyFile by lazy {
        File(lotteryDataDir, "history.csv")
    }
    
    /**
     * 执行完整的自动预测流程
     * @param onProgress 进度回调 (阶段, 进度, 描述)
     * @return 自动预测结果
     */
    suspend fun performAutoPrediction(
        onProgress: (AutoPredictionStage, Int, String) -> Unit = { _, _, _ -> }
    ): AutoPredictionResult {
        return withContext(Dispatchers.IO) {
            try {
                // 阶段1：检查数据状态
                onProgress(AutoPredictionStage.CHECKING_DATA, 0, "检查本地数据状态...")
                val dataStatus = dataVersionManager.getDataStatus()
                
                var historicalData = mutableListOf<ZodiacData>()
                var needsUpdate = false
                
                if (dataStatus.hasData) {
                    // 加载本地数据
                    onProgress(AutoPredictionStage.LOADING_DATA, 20, "加载本地历史数据...")
                    historicalData = loadLocalData().toMutableList()
                    
                    // 检查是否需要更新
                    onProgress(AutoPredictionStage.CHECKING_UPDATES, 40, "检查数据更新...")
                    val updateCheck = dataVersionManager.checkForUpdates()
                    needsUpdate = updateCheck.first
                    
                    onProgress(AutoPredictionStage.CHECKING_UPDATES, 50, updateCheck.second)
                } else {
                    needsUpdate = true
                    onProgress(AutoPredictionStage.CHECKING_DATA, 30, "本地无数据，需要下载")
                }
                
                // 阶段2：下载更新数据（如果需要）
                if (needsUpdate) {
                    onProgress(AutoPredictionStage.DOWNLOADING_DATA, 60, "开始下载数据...")
                    
                    val downloadResult = smartDownloader.smartDownload { current, total, desc ->
                        val progress = 60 + (current * 20 / total)
                        onProgress(AutoPredictionStage.DOWNLOADING_DATA, progress, desc)
                    }
                    
                    if (downloadResult.success) {
                        // 合并新数据
                        if (downloadResult.newDataList.isNotEmpty()) {
                            // 如果是全量下载，替换所有数据
                            if (historicalData.isEmpty()) {
                                historicalData = downloadResult.newDataList.toMutableList()
                            } else {
                                // 增量更新：添加新数据并去重
                                val existingPeriods = historicalData.map { it.expect }.toSet()
                                val newData = downloadResult.newDataList.filter { it.expect !in existingPeriods }
                                historicalData.addAll(0, newData) // 添加到开头（最新的）
                            }
                            
                            // 保存到本地文件
                            saveDataToFile(historicalData)
                        }
                        
                        onProgress(AutoPredictionStage.DOWNLOADING_DATA, 80, downloadResult.message)
                    } else {
                        // 下载失败，但如果有本地数据，继续使用本地数据进行预测
                        if (historicalData.isEmpty()) {
                            return@withContext AutoPredictionResult.failure("数据下载失败且无本地数据：${downloadResult.message}")
                        }
                        onProgress(AutoPredictionStage.DOWNLOADING_DATA, 80, "下载失败，使用本地数据：${downloadResult.message}")
                    }
                } else {
                    onProgress(AutoPredictionStage.DOWNLOADING_DATA, 80, "数据已是最新，跳过下载")
                }
                
                // 阶段3：执行预测计算
                if (historicalData.isEmpty()) {
                    return@withContext AutoPredictionResult.failure("无可用数据进行预测")
                }
                
                onProgress(AutoPredictionStage.CALCULATING_PREDICTION, 85, "开始预测计算...")
                
                val predictionResult = predictionEngine.performPrediction(historicalData)
                
                onProgress(AutoPredictionStage.CALCULATING_PREDICTION, 95, "预测计算完成")
                
                // 阶段4：完成
                onProgress(AutoPredictionStage.COMPLETED, 100, "自动预测流程完成")
                
                AutoPredictionResult(
                    success = true,
                    message = "自动预测完成",
                    dataStatus = dataVersionManager.getDataStatus(),
                    predictionResult = predictionResult,
                    totalDataCount = historicalData.size,
                    lastUpdateInfo = if (needsUpdate) "数据已更新" else "使用本地数据"
                )
                
            } catch (e: Exception) {
                AutoPredictionResult.failure("自动预测失败：${e.message}")
            }
        }
    }
    
    /**
     * 加载本地数据
     */
    private suspend fun loadLocalData(): List<ZodiacData> {
        return withContext(Dispatchers.IO) {
            try {
                if (!historyFile.exists()) {
                    return@withContext emptyList()
                }
                
                val dataList = mutableListOf<ZodiacData>()
                val reader = BufferedReader(FileReader(historyFile))
                
                // 跳过表头
                reader.readLine()
                
                var line = reader.readLine()
                while (line != null) {
                    try {
                        val parts = line.split(",")
                        if (parts.size >= 5) {
                            val expect = parts[0].trim()
                            val openTime = parts[1].trim()
                            val openCode = parts[2].trim()
                            val wave = parts[3].trim()
                            val zodiacStr = parts[4].trim()

                            val zodiacData = ZodiacData(
                                expect = expect,
                                openTime = openTime,
                                openCode = openCode,
                                wave = wave,
                                zodiac = zodiacStr.split(",").map { it.trim() }
                            )

                            dataList.add(zodiacData)
                        }
                    } catch (e: Exception) {
                        // 跳过解析失败的行
                    }
                    line = reader.readLine()
                }
                
                reader.close()
                dataList
                
            } catch (e: Exception) {
                emptyList()
            }
        }
    }
    
    /**
     * 保存数据到文件
     */
    private suspend fun saveDataToFile(dataList: List<ZodiacData>) {
        withContext(Dispatchers.IO) {
            try {
                val csvWriter = historyFile.bufferedWriter()
                
                // 写入CSV表头
                csvWriter.write("期数,开奖时间,开奖号码,波段,生肖\n")
                
                // 写入所有记录
                for (data in dataList) {
                    csvWriter.write("${data.expect},${data.openTime},${data.openCode},${data.wave},${data.zodiac.joinToString(",")}\n")
                }
                
                csvWriter.close()
            } catch (e: Exception) {
                // 保存失败，记录错误但不中断流程
            }
        }
    }
    
    /**
     * 获取数据状态
     */
    fun getDataStatus() = dataVersionManager.getDataStatus()
}

/**
 * 自动预测阶段
 */
enum class AutoPredictionStage {
    CHECKING_DATA,      // 检查数据
    LOADING_DATA,       // 加载数据
    CHECKING_UPDATES,   // 检查更新
    DOWNLOADING_DATA,   // 下载数据
    CALCULATING_PREDICTION, // 计算预测
    COMPLETED           // 完成
}

/**
 * 自动预测结果
 */
data class AutoPredictionResult(
    val success: Boolean,
    val message: String,
    val dataStatus: com.example.myxuanmazhushou.data.DataStatus,
    val predictionResult: PredictionResult,
    val totalDataCount: Int,
    val lastUpdateInfo: String
) {
    companion object {
        fun failure(message: String) = AutoPredictionResult(
            success = false,
            message = message,
            dataStatus = com.example.myxuanmazhushou.data.DataStatus(false, 0, null, 0, "未知"),
            predictionResult = PredictionResult.empty(message),
            totalDataCount = 0,
            lastUpdateInfo = "失败"
        )
    }
}
