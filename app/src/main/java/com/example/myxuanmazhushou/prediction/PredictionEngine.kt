package com.example.myxuanmazhushou.prediction

import com.example.myxuanmazhushou.stats.ZodiacStatsEngine
import com.example.myxuanmazhushou.utils.ZodiacData
import com.example.myxuanmazhushou.utils.ZodiacUtils
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.text.SimpleDateFormat
import java.util.*
import kotlin.math.abs

/**
 * 自动预测计算引擎
 * 基于历史数据实现自动预测算法，包括生肖预测、号码预测等
 */
class PredictionEngine {
    
    private val statsEngine = ZodiacStatsEngine()
    
    /**
     * 执行完整的预测分析
     * @param historicalData 历史数据
     * @return 预测结果
     */
    suspend fun performPrediction(historicalData: List<ZodiacData>): PredictionResult {
        return withContext(Dispatchers.Default) {
            if (historicalData.isEmpty()) {
                return@withContext PredictionResult.empty("历史数据为空")
            }
            
            try {
                // 1. 生肖预测
                val zodiacPrediction = predictZodiac(historicalData)
                
                // 2. 号码预测
                val numberPrediction = predictNumbers(historicalData)
                
                // 3. 波色预测
                val colorPrediction = predictColors(historicalData)
                
                // 4. 趋势分析
                val trendAnalysis = analyzeTrends(historicalData)
                
                PredictionResult(
                    success = true,
                    message = "预测分析完成",
                    zodiacPrediction = zodiacPrediction,
                    numberPrediction = numberPrediction,
                    colorPrediction = colorPrediction,
                    trendAnalysis = trendAnalysis,
                    confidence = calculateOverallConfidence(zodiacPrediction, numberPrediction, colorPrediction)
                )
                
            } catch (e: Exception) {
                PredictionResult.empty("预测计算失败：${e.message}")
            }
        }
    }
    
    /**
     * 预测生肖
     */
    private fun predictZodiac(historicalData: List<ZodiacData>): ZodiacPrediction {
        // 统计特码生肖频率
        val zodiacStats = statsEngine.countSpecialZodiacOccurrences(historicalData)
        
        // 分析最近趋势（最近20期）
        val recentData = historicalData.take(20)
        val recentZodiacStats = statsEngine.countSpecialZodiacOccurrences(recentData)
        
        // 分析周期性规律
        val cyclicalAnalysis = analyzeCyclicalPattern(historicalData)
        
        // 综合分析得出推荐生肖
        val recommendations = mutableListOf<ZodiacRecommendation>()
        
        // 基于频率的推荐（选择出现频率适中的生肖）
        val avgFrequency = zodiacStats.values.average()
        zodiacStats.forEach { (zodiac, count) ->
            val frequency = count.toDouble() / historicalData.size
            val recentFrequency = recentZodiacStats.getOrDefault(zodiac, 0).toDouble() / recentData.size
            
            // 计算推荐度（综合历史频率和最近趋势）
            val score = calculateZodiacScore(frequency, recentFrequency, avgFrequency, count)
            
            if (score > 0.3) { // 阈值可调整
                recommendations.add(
                    ZodiacRecommendation(
                        zodiac = zodiac,
                        confidence = score,
                        reason = buildZodiacReason(frequency, recentFrequency, count)
                    )
                )
            }
        }
        
        // 按推荐度排序
        recommendations.sortByDescending { it.confidence }
        
        return ZodiacPrediction(
            recommendations = recommendations.take(5), // 取前5个推荐
            cyclicalPattern = cyclicalAnalysis,
            totalAnalyzedPeriods = historicalData.size
        )
    }
    
    /**
     * 预测号码
     */
    private fun predictNumbers(historicalData: List<ZodiacData>): NumberPrediction {
        // 统计特码号码频率
        val numberStats = statsEngine.countSpecialNumberOccurrences(historicalData)
        
        // 分析号码分布规律
        val distributionAnalysis = analyzeNumberDistribution(historicalData)
        
        // 分析连号规律
        val consecutiveAnalysis = analyzeConsecutiveNumbers(historicalData)
        
        // 生成推荐号码
        val recommendations = mutableListOf<NumberRecommendation>()
        
        // 基于统计和规律分析推荐号码
        numberStats.forEach { (number, count) ->
            val frequency = count.toDouble() / historicalData.size
            val score = calculateNumberScore(number, frequency, distributionAnalysis, consecutiveAnalysis)
            
            if (score > 0.2) { // 阈值可调整
                recommendations.add(
                    NumberRecommendation(
                        number = number,
                        confidence = score,
                        reason = buildNumberReason(number, frequency, count)
                    )
                )
            }
        }
        
        // 按推荐度排序
        recommendations.sortByDescending { it.confidence }
        
        return NumberPrediction(
            recommendations = recommendations.take(10), // 取前10个推荐
            distributionAnalysis = distributionAnalysis,
            totalAnalyzedPeriods = historicalData.size
        )
    }
    
    /**
     * 预测波色
     */
    private fun predictColors(historicalData: List<ZodiacData>): ColorPrediction {
        val colorStats = mutableMapOf<String, Int>()
        
        historicalData.forEach { data ->
            val numbers = parseNumbers(data.openCode)
            if (numbers.size >= 7) {
                val specialNumber = numbers[6]
                val color = when (specialNumber) {
                    in ZodiacUtils.RED_NUMBERS -> "红"
                    in ZodiacUtils.GREEN_NUMBERS -> "绿"
                    else -> "蓝"
                }
                colorStats[color] = colorStats.getOrDefault(color, 0) + 1
            }
        }
        
        val recommendations = colorStats.map { (color, count) ->
            val frequency = count.toDouble() / historicalData.size
            ColorRecommendation(
                color = color,
                confidence = frequency,
                reason = "历史出现 $count 次，频率 ${String.format("%.1f", frequency * 100)}%"
            )
        }.sortedByDescending { it.confidence }
        
        return ColorPrediction(
            recommendations = recommendations,
            totalAnalyzedPeriods = historicalData.size
        )
    }
    
    /**
     * 分析趋势
     */
    private fun analyzeTrends(historicalData: List<ZodiacData>): TrendAnalysis {
        val recentTrends = mutableListOf<String>()
        
        // 分析最近的生肖趋势
        val recent10 = historicalData.take(10)
        val recent10Zodiacs = recent10.mapNotNull { data ->
            val numbers = parseNumbers(data.openCode)
            if (numbers.size >= 7) {
                ZodiacUtils.getZodiacForNumber(numbers[6])
            } else null
        }
        
        if (recent10Zodiacs.size >= 3) {
            val zodiacCounts = recent10Zodiacs.groupingBy { it }.eachCount()
            val mostFrequent = zodiacCounts.maxByOrNull { it.value }
            if (mostFrequent != null && mostFrequent.value >= 2) {
                recentTrends.add("最近10期中，${mostFrequent.key}出现${mostFrequent.value}次，呈现热门趋势")
            }
        }
        
        // 分析号码大小趋势
        val recentNumbers = recent10.mapNotNull { data ->
            val numbers = parseNumbers(data.openCode)
            if (numbers.size >= 7) numbers[6] else null
        }
        
        if (recentNumbers.isNotEmpty()) {
            val bigCount = recentNumbers.count { it >= 25 }
            val smallCount = recentNumbers.count { it < 25 }
            
            if (bigCount > smallCount * 1.5) {
                recentTrends.add("最近特码偏向大数（≥25）")
            } else if (smallCount > bigCount * 1.5) {
                recentTrends.add("最近特码偏向小数（<25）")
            }
        }
        
        return TrendAnalysis(
            recentTrends = recentTrends,
            analyzedPeriods = recent10.size
        )
    }
    
    // 辅助方法
    private fun parseNumbers(openCode: String): List<Int> {
        return try {
            openCode.split(",").map { it.trim().toInt() }
        } catch (e: Exception) {
            emptyList()
        }
    }
    
    private fun analyzeCyclicalPattern(historicalData: List<ZodiacData>): String {
        // 简单的周期性分析
        return "基于${historicalData.size}期数据的周期性分析"
    }
    
    private fun calculateZodiacScore(frequency: Double, recentFrequency: Double, avgFrequency: Double, count: Int): Double {
        // 综合评分算法
        val frequencyScore = if (frequency > 0.05 && frequency < 0.15) 0.8 else 0.4
        val recentScore = if (recentFrequency > 0.1) 0.7 else 0.3
        val balanceScore = if (abs(frequency - avgFrequency) < 0.02) 0.6 else 0.3

        return (frequencyScore + recentScore + balanceScore) / 3
    }
    
    private fun buildZodiacReason(frequency: Double, recentFrequency: Double, count: Int): String {
        return "历史频率${String.format("%.1f", frequency * 100)}%，最近频率${String.format("%.1f", recentFrequency * 100)}%"
    }
    
    private fun analyzeNumberDistribution(historicalData: List<ZodiacData>): String {
        return "号码分布分析完成"
    }
    
    private fun analyzeConsecutiveNumbers(historicalData: List<ZodiacData>): String {
        return "连号分析完成"
    }
    
    private fun calculateNumberScore(number: Int, frequency: Double, distributionAnalysis: String, consecutiveAnalysis: String): Double {
        // 简化的号码评分
        return if (frequency > 0.01 && frequency < 0.05) 0.6 else 0.3
    }
    
    private fun buildNumberReason(number: Int, frequency: Double, count: Int): String {
        return "出现${count}次，频率${String.format("%.1f", frequency * 100)}%"
    }
    
    private fun calculateOverallConfidence(zodiac: ZodiacPrediction, number: NumberPrediction, color: ColorPrediction): Double {
        val zodiacConf = zodiac.recommendations.firstOrNull()?.confidence ?: 0.0
        val numberConf = number.recommendations.firstOrNull()?.confidence ?: 0.0
        val colorConf = color.recommendations.firstOrNull()?.confidence ?: 0.0
        
        return (zodiacConf + numberConf + colorConf) / 3
    }
}

/**
 * 预测结果
 */
data class PredictionResult(
    val success: Boolean,
    val message: String,
    val zodiacPrediction: ZodiacPrediction,
    val numberPrediction: NumberPrediction,
    val colorPrediction: ColorPrediction,
    val trendAnalysis: TrendAnalysis,
    val confidence: Double
) {
    companion object {
        fun empty(message: String) = PredictionResult(
            success = false,
            message = message,
            zodiacPrediction = ZodiacPrediction(emptyList(), "", 0),
            numberPrediction = NumberPrediction(emptyList(), "", 0),
            colorPrediction = ColorPrediction(emptyList(), 0),
            trendAnalysis = TrendAnalysis(emptyList(), 0),
            confidence = 0.0
        )
    }
}

/**
 * 生肖预测结果
 */
data class ZodiacPrediction(
    val recommendations: List<ZodiacRecommendation>,
    val cyclicalPattern: String,
    val totalAnalyzedPeriods: Int
)

/**
 * 生肖推荐
 */
data class ZodiacRecommendation(
    val zodiac: String,
    val confidence: Double,
    val reason: String
)

/**
 * 号码预测结果
 */
data class NumberPrediction(
    val recommendations: List<NumberRecommendation>,
    val distributionAnalysis: String,
    val totalAnalyzedPeriods: Int
)

/**
 * 号码推荐
 */
data class NumberRecommendation(
    val number: Int,
    val confidence: Double,
    val reason: String
)

/**
 * 波色预测结果
 */
data class ColorPrediction(
    val recommendations: List<ColorRecommendation>,
    val totalAnalyzedPeriods: Int
)

/**
 * 波色推荐
 */
data class ColorRecommendation(
    val color: String,
    val confidence: Double,
    val reason: String
)

/**
 * 趋势分析结果
 */
data class TrendAnalysis(
    val recentTrends: List<String>,
    val analyzedPeriods: Int
)
