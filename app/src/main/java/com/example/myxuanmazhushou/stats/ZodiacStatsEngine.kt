package com.example.myxuanmazhushou.stats

import com.example.myxuanmazhushou.utils.ZodiacData
import com.example.myxuanmazhushou.utils.ZodiacUtils
import java.text.SimpleDateFormat
import java.util.*

/**
 * 生肖统计引擎
 * 提供各种生肖统计分析功能
 */
class ZodiacStatsEngine {
    
    // 十二生肖列表
    val zodiacList = ZodiacUtils.ZODIAC_ORDER
    
    // 日期格式化
    private val yearFormat = SimpleDateFormat("yyyy", Locale.CHINA)
    private val monthFormat = SimpleDateFormat("yyyy-MM", Locale.CHINA)
    
    /**
     * 统计第七个号码的生肖出现次数
     * @param dataList 历史数据列表
     * @return 生肖出现次数统计
     */
    fun countSeventhZodiacOccurrences(dataList: List<ZodiacData>): Map<String, Int> {
        // 初始化结果Map
        val result = zodiacList.associateWith { 0 }.toMutableMap()
        
        // 遍历每条历史数据
        dataList.forEach { data ->
            // 解析号码
            try {
                val numbers = parseNumbers(data.openCode)
                if (numbers.size >= 7) {
                    // 获取第七个号码
                    val seventhNumber = numbers[6]
                    // 获取对应生肖
                    ZodiacUtils.getZodiacForNumber(seventhNumber)?.let { zodiac ->
                        // 更新计数
                        result[zodiac] = result.getOrDefault(zodiac, 0) + 1
                    }
                }
            } catch (e: Exception) {
                // 解析失败，跳过
            }
        }
        
        return result
    }
    
    /**
     * 统计特码生肖出现次数（特码即第七个号码）
     * @param dataList 历史数据列表
     * @return 特码生肖出现次数统计
     */
    fun countSpecialZodiacOccurrences(dataList: List<ZodiacData>): Map<String, Int> {
        // 初始化结果Map，确保所有生肖都在结果中
        val result = zodiacList.associateWith { 0 }.toMutableMap()
        
        // 打印原始数据的数量，用于调试
        println("开始统计特码生肖，共有 ${dataList.size} 条记录")
        
        // 遍历每条历史数据
        var validCount = 0
        dataList.forEach { data ->
            // 解析号码
            try {
                val numbers = parseNumbers(data.openCode)
                if (numbers.size >= 7) {
                    // 获取第七个号码（特码）
                    val specialNumber = numbers[6]
                    // 打印特码，方便调试
                    println("期号: ${data.expect}, 特码: $specialNumber")
                    
                    // 获取对应生肖
                    val zodiac = ZodiacUtils.getZodiacForNumber(specialNumber)
                    if (zodiac != null) {
                        // 更新计数
                        result[zodiac] = result.getOrDefault(zodiac, 0) + 1
                        validCount++
                    } else {
                        println("警告: 特码 $specialNumber 无法找到对应生肖")
                    }
                } else {
                    println("警告: 开奖号码 ${data.openCode} 格式不正确，无法提取特码")
                }
            } catch (e: Exception) {
                println("解析开奖号码时出错: ${data.openCode}, 错误: ${e.message}")
            }
        }
        
        println("成功统计特码生肖 $validCount 条，生肖统计结果: $result")
        return result
    }
    
    /**
     * 按年份统计特码生肖的出现次数
     * @param dataList 历史数据列表
     * @return 按年份分组的特码生肖统计，格式为：年份 -> (生肖 -> 次数)
     */
    fun countSpecialZodiacByYear(dataList: List<ZodiacData>): Map<String, Map<String, Int>> {
        // 结果Map：年份 -> (生肖 -> 次数)
        val result = mutableMapOf<String, MutableMap<String, Int>>()
        
        println("开始按年份统计特码生肖，共有 ${dataList.size} 条记录")
        
        // 遍历每条历史数据
        dataList.forEach { data ->
            try {
                // 解析开奖时间，提取年份
                val date = SimpleDateFormat("yyyy-MM-dd", Locale.CHINA).parse(data.openTime)
                val year = yearFormat.format(date ?: Date())
                
                // 确保年份在结果Map中存在
                if (!result.containsKey(year)) {
                    result[year] = zodiacList.associateWith { 0 }.toMutableMap()
                }
                
                // 解析号码并统计特码生肖
                val numbers = parseNumbers(data.openCode)
                if (numbers.size >= 7) {
                    val specialNumber = numbers[6] // 第七个号码为特码
                    ZodiacUtils.getZodiacForNumber(specialNumber)?.let { zodiac ->
                        val yearMap = result[year] ?: return@forEach
                        yearMap[zodiac] = yearMap.getOrDefault(zodiac, 0) + 1
                    }
                }
            } catch (e: Exception) {
                // 日期或号码解析失败，跳过该记录
                println("按年份统计时出错，期号: ${data.expect}, 错误: ${e.message}")
            }
        }
        
        // 打印每个年份的统计数据数量
        result.forEach { (year, zodiacMap) ->
            val count = zodiacMap.values.sum()
            println("$year 年特码生肖统计: $count 条")
        }
        
        return result
    }
    
    /**
     * 按月份统计特码生肖的出现次数
     * @param dataList 历史数据列表
     * @return 按月份分组的特码生肖统计，格式为：年月 -> (生肖 -> 次数)
     */
    fun countSpecialZodiacByMonth(dataList: List<ZodiacData>): Map<String, Map<String, Int>> {
        // 结果Map：年月 -> (生肖 -> 次数)
        val result = mutableMapOf<String, MutableMap<String, Int>>()
        
        println("开始按月份统计特码生肖，共有 ${dataList.size} 条记录")
        
        // 遍历每条历史数据
        dataList.forEach { data ->
            try {
                // 解析开奖时间，提取年月
                val date = SimpleDateFormat("yyyy-MM-dd", Locale.CHINA).parse(data.openTime)
                val month = monthFormat.format(date ?: Date())
                
                // 确保年月在结果Map中存在
                if (!result.containsKey(month)) {
                    result[month] = zodiacList.associateWith { 0 }.toMutableMap()
                }
                
                // 解析号码并统计特码生肖
                val numbers = parseNumbers(data.openCode)
                if (numbers.size >= 7) {
                    val specialNumber = numbers[6] // 第七个号码为特码
                    ZodiacUtils.getZodiacForNumber(specialNumber)?.let { zodiac ->
                        val monthMap = result[month] ?: return@forEach
                        monthMap[zodiac] = monthMap.getOrDefault(zodiac, 0) + 1
                    }
                }
            } catch (e: Exception) {
                // 日期或号码解析失败，跳过该记录
                println("按月份统计时出错，期号: ${data.expect}, 错误: ${e.message}")
            }
        }
        
        // 打印最近几个月的统计数据数量
        result.entries.sortedByDescending { it.key }.take(12).forEach { (month, zodiacMap) ->
            val count = zodiacMap.values.sum()
            println("$month 月特码生肖统计: $count 条")
        }
        
        return result
    }
    
    /**
     * 按总量统计各个生肖的开出次数
     * @param dataList 历史数据列表
     * @return 各个生肖出现的总次数
     */
    fun countTotalZodiacOccurrences(dataList: List<ZodiacData>): Map<String, Int> {
        // 初始化结果Map
        val result = zodiacList.associateWith { 0 }.toMutableMap()
        
        // 遍历每条历史数据
        dataList.forEach { data ->
            try {
                val numbers = parseNumbers(data.openCode)
                numbers.forEach { number ->
                    ZodiacUtils.getZodiacForNumber(number)?.let { zodiac ->
                        result[zodiac] = result.getOrDefault(zodiac, 0) + 1
                    }
                }
            } catch (e: Exception) {
                // 解析失败，跳过
            }
        }
        
        return result
    }
    
    /**
     * 按年份统计各个生肖的开出次数
     * @param dataList 历史数据列表
     * @return 按年份分组的生肖统计，格式为：年份 -> (生肖 -> 次数)
     */
    fun countZodiacOccurrencesByYear(dataList: List<ZodiacData>): Map<String, Map<String, Int>> {
        // 结果Map：年份 -> (生肖 -> 次数)
        val result = mutableMapOf<String, MutableMap<String, Int>>()
        
        // 遍历每条历史数据
        dataList.forEach { data ->
            try {
                // 解析开奖时间，提取年份
                val date = SimpleDateFormat("yyyy-MM-dd", Locale.CHINA).parse(data.openTime)
                val year = yearFormat.format(date ?: Date())
                
                // 确保年份在结果Map中存在
                if (!result.containsKey(year)) {
                    result[year] = zodiacList.associateWith { 0 }.toMutableMap()
                }
                
                // 解析号码并统计生肖
                val numbers = parseNumbers(data.openCode)
                numbers.forEach { number ->
                    ZodiacUtils.getZodiacForNumber(number)?.let { zodiac ->
                        val yearMap = result[year] ?: return@forEach
                        yearMap[zodiac] = yearMap.getOrDefault(zodiac, 0) + 1
                    }
                }
            } catch (e: Exception) {
                // 日期或号码解析失败，跳过该记录
            }
        }
        
        return result
    }
    
    /**
     * 按月份统计各个生肖的开出次数
     * @param dataList 历史数据列表
     * @return 按月份分组的生肖统计，格式为：年月 -> (生肖 -> 次数)
     */
    fun countZodiacOccurrencesByMonth(dataList: List<ZodiacData>): Map<String, Map<String, Int>> {
        // 结果Map：年月 -> (生肖 -> 次数)
        val result = mutableMapOf<String, MutableMap<String, Int>>()
        
        // 遍历每条历史数据
        dataList.forEach { data ->
            try {
                // 解析开奖时间，提取年月
                val date = SimpleDateFormat("yyyy-MM-dd", Locale.CHINA).parse(data.openTime)
                val month = monthFormat.format(date ?: Date())
                
                // 确保年月在结果Map中存在
                if (!result.containsKey(month)) {
                    result[month] = zodiacList.associateWith { 0 }.toMutableMap()
                }
                
                // 解析号码并统计生肖
                val numbers = parseNumbers(data.openCode)
                numbers.forEach { number ->
                    ZodiacUtils.getZodiacForNumber(number)?.let { zodiac ->
                        val monthMap = result[month] ?: return@forEach
                        monthMap[zodiac] = monthMap.getOrDefault(zodiac, 0) + 1
                    }
                }
            } catch (e: Exception) {
                // 日期或号码解析失败，跳过该记录
            }
        }
        
        return result
    }
    
    /**
     * 获取特定时间范围内的数据子集
     * @param dataList 完整的数据列表
     * @param startDate 开始日期（yyyy-MM-dd格式）
     * @param endDate 结束日期（yyyy-MM-dd格式）
     * @return 过滤后的数据列表
     */
    fun getDataInTimeRange(dataList: List<ZodiacData>, startDate: String, endDate: String): List<ZodiacData> {
        val dateFormat = SimpleDateFormat("yyyy-MM-dd", Locale.CHINA)
        
        try {
            val startDateTime = dateFormat.parse(startDate)?.time ?: 0
            val endDateTime = dateFormat.parse(endDate)?.time ?: Long.MAX_VALUE
            
            return dataList.filter { data ->
                try {
                    val dataTime = dateFormat.parse(data.openTime)?.time ?: 0
                    dataTime in startDateTime..endDateTime
                } catch (e: Exception) {
                    false
                }
            }
        } catch (e: Exception) {
            return emptyList()
        }
    }
    
    /**
     * 获取最近N期的数据
     * @param dataList 完整的数据列表
     * @param count 期数
     * @return 最近N期数据
     */
    fun getLatestData(dataList: List<ZodiacData>, count: Int): List<ZodiacData> {
        // 确保dataList已按期号降序排序
        return dataList.take(count)
    }
    
    /**
     * 获取生肖出现频率走势数据
     * 为每个生肖计算在最近N期中的出现频率变化
     * @param dataList 历史数据列表
     * @param periodCount 每个周期的期数
     * @param totalPeriods 要分析的周期总数
     * @return 生肖频率走势数据 Map<生肖, List<频率>>
     */
    fun getZodiacTrends(dataList: List<ZodiacData>, periodCount: Int, totalPeriods: Int): Map<String, List<Double>> {
        // 初始化结果Map
        val result = zodiacList.associateWith { mutableListOf<Double>() }.toMutableMap()
        
        // 确保数据量足够
        if (dataList.size < periodCount * totalPeriods) {
            return result
        }
        
        // 分析每个周期
        for (i in 0 until totalPeriods) {
            // 获取当前周期的数据子集
            val startIndex = i * periodCount
            val endIndex = startIndex + periodCount
            val periodData = dataList.subList(startIndex, endIndex)
            
            // 统计当前周期内各个生肖的出现次数
            val periodStats = countTotalZodiacOccurrences(periodData)
            
            // 计算频率（次数/总期数）并添加到结果中
            zodiacList.forEach { zodiac ->
                val frequency = periodStats.getOrDefault(zodiac, 0).toDouble() / periodCount
                result[zodiac]?.add(frequency)
            }
        }
        
        return result
    }
    
    /**
     * 根据特定生肖和条件筛选历史数据
     * @param dataList 历史数据列表
     * @param targetZodiac 目标生肖
     * @return 符合条件的历史数据列表
     */
    fun filterDataByZodiac(dataList: List<ZodiacData>, targetZodiac: String): List<ZodiacData> {
        return dataList.filter { data ->
            try {
                val numbers = parseNumbers(data.openCode)
                numbers.any { number ->
                    ZodiacUtils.getZodiacForNumber(number) == targetZodiac
                }
            } catch (e: Exception) {
                false
            }
        }
    }
    
    /**
     * 统计各个号码位置的生肖出现频率
     * @param dataList 历史数据列表
     * @return 按号码位置的生肖统计，格式为：位置索引 -> (生肖 -> 次数)
     */
    fun countZodiacFrequencies(dataList: List<ZodiacData>): Map<Int, Map<String, Int>> {
        val result = mutableMapOf<Int, MutableMap<String, Int>>()
        
        // 初始化结果集
        for (position in 1..7) {
            result[position] = zodiacList.associateWith { 0 }.toMutableMap()
        }
        
        // 统计每个位置的生肖出现次数
        dataList.forEach { data ->
            try {
                val numbers = parseNumbers(data.openCode)
                numbers.forEachIndexed { index, number ->
                    val position = index + 1
                    if (position <= 7) { // 只统计前7个位置
                        ZodiacUtils.getZodiacForNumber(number)?.let { zodiac ->
                            result[position]?.let { positionMap ->
                                positionMap[zodiac] = positionMap.getOrDefault(zodiac, 0) + 1
                            }
                        }
                    }
                }
            } catch (e: Exception) {
                // 解析失败，跳过
            }
        }
        
        return result
    }
    
    /**
     * 计算每个生肖总共出现的次数
     * @param dataList 历史数据列表
     * @return 各个生肖的总出现次数
     */
    fun countTotalTimesEachZodiacAppeared(dataList: List<ZodiacData>): Map<String, Int> {
        val result = zodiacList.associateWith { 0 }.toMutableMap()
        
        dataList.forEach { data ->
            try {
                val numbers = parseNumbers(data.openCode)
                numbers.forEach { number ->
                    ZodiacUtils.getZodiacForNumber(number)?.let { zodiac ->
                        result[zodiac] = result.getOrDefault(zodiac, 0) + 1
                    }
                }
            } catch (e: Exception) {
                // 解析失败，跳过
            }
        }
        
        return result
    }
    
    /**
     * 计算生肖连续出现和连续未出现的最大次数
     * @param dataList 历史数据列表
     * @return 生肖的连续统计数据
     */
    fun calculateConsecutiveStats(dataList: List<ZodiacData>): Map<String, Map<String, Int>> {
        val result = mutableMapOf<String, Map<String, Int>>()
        
        zodiacList.forEach { zodiac ->
            val consecutive = mutableMapOf<String, Int>()
            var maxConsAppear = 0
            var currentConsAppear = 0
            var maxConsAbsent = 0
            var currentConsAbsent = 0
            
            dataList.forEach { data ->
                try {
                    val numbers = parseNumbers(data.openCode)
                    val isPresent = numbers.any { number ->
                        ZodiacUtils.getZodiacForNumber(number) == zodiac
                    }
                    
                    if (isPresent) {
                        currentConsAppear++
                        currentConsAbsent = 0
                        if (currentConsAppear > maxConsAppear) {
                            maxConsAppear = currentConsAppear
                        }
                    } else {
                        currentConsAbsent++
                        currentConsAppear = 0
                        if (currentConsAbsent > maxConsAbsent) {
                            maxConsAbsent = currentConsAbsent
                        }
                    }
                } catch (e: Exception) {
                    // 解析失败，跳过
                }
            }
            
            consecutive["maxConsAppear"] = maxConsAppear
            consecutive["maxConsAbsent"] = maxConsAbsent
            result[zodiac] = consecutive
        }
        
        return result
    }
    
    /**
     * 解析开奖号码字符串
     * @param openCode 开奖号码字符串，如"01,02,03,04,05,06+07"
     * @return 解析后的号码列表
     */
    private fun parseNumbers(openCode: String): List<Int> {
        if (openCode.isEmpty()) {
            println("解析开奖号码: 号码为空")
            return emptyList()
        }
        
        println("解析开奖号码: $openCode")
        
        try {
            // 尝试多种格式解析
            if (openCode.contains("+")) {
                // 标准格式 01,02,03,04,05,06+07
                val parts = openCode.split("+")
                val mainNumbers = parts[0].split(",").map { it.trim().toInt() }
                val specialNumber = if (parts.size > 1) listOf(parts[1].trim().toInt()) else emptyList()
                val result = mainNumbers + specialNumber
                println("解析结果(+格式): $result, 特码: ${if(result.size >= 7) result[6] else "无"}")
                return result
            } else if (openCode.contains(",")) {
                // 逗号分隔格式 01,02,03,04,05,06,07
                val numbers = openCode.split(",").map { it.trim().toInt() }
                println("解析结果(,格式): $numbers, 特码: ${if(numbers.size >= 7) numbers[6] else "无"}")
                return numbers
            } else {
                // 尝试空格分隔格式 01 02 03 04 05 06 07
                if (openCode.contains(" ")) {
                    val numbers = openCode.split(" ").filter { it.isNotBlank() }.map { it.trim().toInt() }
                    println("解析结果(空格格式): $numbers, 特码: ${if(numbers.size >= 7) numbers[6] else "无"}")
                    return numbers
                }
                
                // 尝试直接转换为整数（单个数字的情况）
                val number = openCode.trim().toInt()
                println("单个数字: $number")
                return listOf(number)
            }
        } catch (e: Exception) {
            println("解析开奖号码失败: $openCode, 错误: ${e.message}")
            return emptyList()
        }
    }
    
    /**
     * 统计特码号码出现次数（特码即第七个号码）
     * @param dataList 历史数据列表
     * @return 特码号码出现次数统计，格式为：号码 -> 次数
     */
    fun countSpecialNumberOccurrences(dataList: List<ZodiacData>): Map<Int, Int> {
        // 初始化结果Map，包含1-49所有可能的号码
        val result = (1..49).associateWith { 0 }.toMutableMap()
        
        println("开始统计特码号码，总数据量: ${dataList.size}")
        var successCount = 0
        var failureCount = 0
        
        // 遍历每条历史数据
        dataList.forEach { data ->
            try {
                val numbers = parseNumbers(data.openCode)
                if (numbers.size >= 7) {
                    // 获取第七个号码（特码）
                    val specialNumber = numbers[6]
                    // 更新计数
                    result[specialNumber] = result.getOrDefault(specialNumber, 0) + 1
                    successCount++
                } else {
                    failureCount++
                    println("警告: 期号 ${data.expect} 的开奖号码不足7个: ${data.openCode}")
                }
            } catch (e: Exception) {
                // 解析失败，跳过
                failureCount++
                println("解析开奖号码出错: ${data.openCode}, 错误: ${e.message}")
            }
        }
        
        println("特码号码统计完成: 成功 $successCount 条，失败 $failureCount 条")
        println("统计结果: ${result.filter { it.value > 0 }}")
        
        return result
    }
    
    /**
     * 按年份统计特码号码的出现次数
     * @param dataList 历史数据列表
     * @return 按年份分组的特码号码统计，格式为：年份 -> (号码 -> 次数)
     */
    fun countSpecialNumberByYear(dataList: List<ZodiacData>): Map<String, Map<Int, Int>> {
        // 结果Map：年份 -> (号码 -> 次数)
        val result = mutableMapOf<String, MutableMap<Int, Int>>()
        
        // 遍历每条历史数据
        dataList.forEach { data ->
            try {
                // 解析开奖时间，提取年份
                val date = SimpleDateFormat("yyyy-MM-dd", Locale.CHINA).parse(data.openTime)
                val year = yearFormat.format(date ?: Date())
                
                // 确保年份在结果Map中存在
                if (!result.containsKey(year)) {
                    result[year] = (1..49).associateWith { 0 }.toMutableMap()
                }
                
                // 解析号码并统计特码
                val numbers = parseNumbers(data.openCode)
                if (numbers.size >= 7) {
                    val specialNumber = numbers[6] // 第七个号码为特码
                    val yearMap = result[year] ?: return@forEach
                    yearMap[specialNumber] = yearMap.getOrDefault(specialNumber, 0) + 1
                }
            } catch (e: Exception) {
                // 日期或号码解析失败，跳过该记录
            }
        }
        
        return result
    }
    
    /**
     * 按月份统计特码号码的出现次数
     * @param dataList 历史数据列表
     * @return 按月份分组的特码号码统计，格式为：年月 -> (号码 -> 次数)
     */
    fun countSpecialNumberByMonth(dataList: List<ZodiacData>): Map<String, Map<Int, Int>> {
        // 结果Map：年月 -> (号码 -> 次数)
        val result = mutableMapOf<String, MutableMap<Int, Int>>()
        
        println("开始按月份统计特码号码，共有 ${dataList.size} 条记录")
        var successCount = 0
        var failureCount = 0
        
        // 遍历每条历史数据
        dataList.forEach { data ->
            try {
                // 检查开奖时间是否有效
                if (data.openTime.isBlank() || !data.openTime.contains("-")) {
                    println("警告: 期号 ${data.expect} 的开奖时间格式无效: ${data.openTime}")
                    failureCount++
                    return@forEach
                }
                
                // 尝试解析开奖时间，提取年月，使用更安全的方法
                val dateFormat = SimpleDateFormat("yyyy-MM-dd", Locale.CHINA)
                try {
                    val date = dateFormat.parse(data.openTime)
                    if (date == null) {
                        println("警告: 期号 ${data.expect} 的开奖时间解析失败: ${data.openTime}")
                        failureCount++
                        return@forEach
                    }
                    
                    val month = monthFormat.format(date)
                    
                    // 确保年月在结果Map中存在
                    if (!result.containsKey(month)) {
                        result[month] = (1..49).associateWith { 0 }.toMutableMap()
                    }
                    
                    // 解析号码并统计特码
                    val numbers = parseNumbers(data.openCode)
                    if (numbers.size >= 7) {
                        val specialNumber = numbers[6] // 第七个号码为特码
                        val monthMap = result[month] ?: return@forEach
                        monthMap[specialNumber] = monthMap.getOrDefault(specialNumber, 0) + 1
                        successCount++
                    } else {
                        println("警告: 期号 ${data.expect} 的开奖号码不足7个: ${data.openCode}")
                        failureCount++
                    }
                } catch (e: Exception) {
                    println("按月统计时日期解析错误，期号: ${data.expect}, 开奖时间: ${data.openTime}, 错误: ${e.message}")
                    failureCount++
                }
            } catch (e: Exception) {
                // 处理整体异常，避免单个数据导致整个统计失败
                failureCount++
                println("按月份统计处理数据出错，期号: ${data.expect}, 错误: ${e.message}")
            }
        }
        
        println("按月统计特码号码完成: 成功处理 $successCount 条，失败 $failureCount 条，共生成 ${result.size} 个月份的统计数据")
        
        return result
    }
} 