package com.example.myxuanmazhushou.utils

import android.content.Context
import android.util.Log
import org.json.JSONArray
import org.json.JSONObject

object ZodiacUtils {
    private const val PREFS_NAME = "zodiac_prefs"
    private const val KEY_ZODIAC_MAPPINGS = "zodiac_mappings"
    private const val KEY_BASE_YEAR = "base_year"

    // 生肖顺序（按照农历年份排序）
    val ZODIAC_ORDER = listOf("鼠", "牛", "虎", "兔", "龙", "蛇", "马", "羊", "猴", "鸡", "狗", "猪")

    // 波色常量定义
    val RED_NUMBERS = setOf(1, 2, 7, 8, 12, 13, 18, 19, 23, 24, 29, 30, 34, 35, 40, 45, 46)
    val BLUE_NUMBERS = setOf(3, 4, 9, 10, 14, 15, 20, 25, 26, 31, 36, 37, 41, 42, 47, 48)
    val GREEN_NUMBERS = setOf(5, 6, 11, 16, 17, 21, 22, 27, 28, 32, 33, 38, 39, 43, 44, 49)

    // 增加家禽、野兽、单肖、双肖的常量
    val DOMESTIC_ANIMALS = setOf("鸡", "狗", "猪", "牛", "马", "羊") // 家禽
    val WILD_ANIMALS = setOf("鼠", "虎", "兔", "龙", "蛇", "猴")     // 野兽
    
    val ODD_ZODIACS = setOf("鼠", "虎", "龙", "马", "猴", "狗")     // 双肖(所有号码为双数的生肖)
    val EVEN_ZODIACS = setOf("牛", "兔", "蛇", "羊", "鸡", "猪")     // 单肖(所有号码为单数的生肖)

    // 字头数映射
    val HEAD_NUMBERS = mapOf(
        "零" to (1..9).toList(),
        "一" to (10..19).toList(),
        "二" to (20..29).toList(),
        "三" to (30..39).toList(),
        "四" to (40..49).toList(),
        "0" to (1..9).toList(),
        "1" to (10..19).toList(),
        "2" to (20..29).toList(),
        "3" to (30..39).toList(),
        "4" to (40..49).toList()
    )

    // 中文数字映射
    val CHINESE_NUMBERS = mapOf(
        "零" to 0, "一" to 1, "二" to 2, "三" to 3, "四" to 4,
        "五" to 5, "六" to 6, "七" to 7, "八" to 8, "九" to 9,
        "十" to 10, "百" to 100, "千" to 1000, "万" to 10000
    )

    // 号码类型映射
    val NUMBER_TYPES = mapOf(
        "大单" to (25..49).filter { it % 2 == 1 }.toList(),
        "大双" to (25..49).filter { it % 2 == 0 }.toList(),
        "小单" to (1..24).filter { it % 2 == 1 }.toList(),
        "小双" to (1..24).filter { it % 2 == 0 }.toList(),
        "单" to (1..49).filter { it % 2 == 1 }.toList(),
        "双" to (1..49).filter { it % 2 == 0 }.toList(),
        "大" to (25..49).toList(),
        "小" to (1..24).toList()
    )

    private var customZodiacMap = mutableMapOf<String, List<Int>>()

    // 获取当前使用的生肖映射
    fun getZodiacMappings(): Map<String, List<Int>> {
        return customZodiacMap
    }

    // 获取当前基准年份
    fun getCurrentBaseYear(context: Context): Int {
        return context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
            .getInt(KEY_BASE_YEAR, 2024) // 默认2024
    }

    // 更新年份并重新生成映射
    fun updateBaseYear(context: Context, year: Int) {
        // 保存新的年份
        context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE).edit()
            .putInt(KEY_BASE_YEAR, year)
            .apply()
        
        // 根据新年份生成生肖映射
        val newMappings = generateZodiacMapping(year)
        
        // 更新内存中的映射
        customZodiacMap.clear()
        customZodiacMap.putAll(newMappings)
        
        // 保存新的映射到 SharedPreferences
        saveToPreferences(context, newMappings)
        
        // 添加日志以便调试
        Log.d("ZodiacUtils", "Updated base year to $year and generated new mappings")
        Log.d("ZodiacUtils", "New mappings: $newMappings")
    }

    // 更新生肖映射
    fun updateZodiacMappings(context: Context, newMappings: Map<String, List<Int>>) {
        customZodiacMap.clear()
        customZodiacMap.putAll(newMappings)
        saveToPreferences(context, newMappings)
    }

    // 加载保存的映射
    fun loadSavedMappings(context: Context) {
        val prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
        val savedMappings = prefs.getString(KEY_ZODIAC_MAPPINGS, null)
        if (savedMappings != null) {
            try {
                customZodiacMap = jsonToMap(savedMappings).toMutableMap()
            } catch (e: Exception) {
                e.printStackTrace()
                // 如果读取失败，使用当前年份生成新的映射
                customZodiacMap = generateZodiacMapping(getCurrentBaseYear(context)).toMutableMap()
            }
        } else {
            // 如果没有保存的映射，使用当前年份生成新的映射
            customZodiacMap = generateZodiacMapping(getCurrentBaseYear(context)).toMutableMap()
        }
    }

    // 根据年份生成生肖映射
    fun generateZodiacMapping(year: Int): Map<String, List<Int>> {
        // 计算当年生肖在序列中的位置
        val yearZodiacIndex = (year - 1900) % 12
        val result = LinkedHashMap<String, List<Int>>() // 使用 LinkedHashMap 保持插入顺序
        
        // 先添加固定顺序的生肖
        ZODIAC_ORDER.forEach { zodiac ->
            result[zodiac] = emptyList()
        }
        
        // 获取当年生肖
        val currentYearZodiac = ZODIAC_ORDER[yearZodiacIndex]
        val currentYearZodiacIndex = ZODIAC_ORDER.indexOf(currentYearZodiac)
        
        // 为每个生肖生成对应的号码
        ZODIAC_ORDER.forEachIndexed { index, zodiac ->
            val numbers = when {
                index == currentYearZodiacIndex -> {
                    // 当年生肖
                    listOf(1, 13, 25, 37, 49).filter { it <= 49 }
                }
                index == (currentYearZodiacIndex + 1) % 12 -> {
                    // 下一年生肖
                    listOf(12, 24, 36, 48).filter { it <= 49 }
                }
                else -> {
                    // 计算其他生肖的起始数字
                    val distance = (index - currentYearZodiacIndex + 12) % 12
                    val startNum = if (distance > 1) {
                        13 - distance
                    } else {
                        2  // 当年生肖的前一个生肖从2开始
                    }
                    listOf(startNum, startNum + 12, startNum + 24, startNum + 36)
                        .filter { it in 1..49 }
                }
            }
            result[zodiac] = numbers
        }
        
        // 添加调试日志
        Log.d("ZodiacUtils", "Year: $year, Current Zodiac: $currentYearZodiac")
        Log.d("ZodiacUtils", "Generated Mappings: $result")
        
        return result
    }

    // 获取指定年份的生肖
    fun getZodiacForYear(year: Int): String {
        val index = (year - 1900) % 12
        return ZODIAC_ORDER[index]
    }

    // 保存到 SharedPreferences
    private fun saveToPreferences(context: Context, mappings: Map<String, List<Int>>) {
        val prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
        prefs.edit().putString(KEY_ZODIAC_MAPPINGS, mapToJson(mappings)).apply()
    }

    // 转换为 JSON
    private fun mapToJson(map: Map<String, List<Int>>): String {
        return JSONObject().apply {
            map.forEach { (zodiac, numbers) ->
                put(zodiac, JSONArray(numbers))
            }
        }.toString()
    }

    // 从 JSON 转换
    private fun jsonToMap(json: String): Map<String, List<Int>> {
        val result = mutableMapOf<String, List<Int>>()
        val jsonObj = JSONObject(json)
        jsonObj.keys().forEach { zodiac ->
            val numbers = mutableListOf<Int>()
            val jsonArray = jsonObj.getJSONArray(zodiac)
            for (i in 0 until jsonArray.length()) {
                numbers.add(jsonArray.getInt(i))
            }
            result[zodiac] = numbers
        }
        return result
    }

    // 获取号码对应的生肖
    fun getZodiacForNumber(number: Int): String? {
        return getZodiacMappings().entries.find { (_, numbers) ->
            numbers.contains(number)
        }?.key
    }
}
